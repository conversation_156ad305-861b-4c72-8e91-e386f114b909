import asyncio
import json
import os
from typing import Dict, Any, Optional, List
from core.memory.redis_context import RedisClient
from core.logging.logger_config import get_module_logger
import openai

class OrchestratorV3:
    def __init__(self, session_id: str, workflow_name: str, state_manager, memory_manager, redis_client: RedisClient):
        self.session_id = session_id
        self.workflow_name = workflow_name
        self.state_manager = state_manager
        self.memory_manager = memory_manager
        self.redis_client = redis_client
        self.logger = get_module_logger("orchestrator_v3", session_id=session_id)
        self.prohibited_actions = None
        self.agent_response_cache: Dict[str, List[Dict[str, Any]]] = {}  # state_id -> list of responses
        self.workflow_summary = None
        self.status = "initialized"
        self.reason = None
        self.key_events = []

        # Track conversation for dialog logging
        self.user_message = ""
        self.ai_response = ""

    async def initialize(self):
        # Cache prohibited actions at start
        if self.prohibited_actions is None:
            self.prohibited_actions = await self.state_manager.getProhibitedActions()
        # Preload agent responses for this session
        await self._populate_agent_response_cache()
        self.logger.info("OrchestratorV3 initialized", action="initialize", layer="orchestrator_v3", step="init")

    async def _populate_agent_response_cache(self):
        """Parse the conversations log and cache agent responses for this session."""
        log_path = os.path.join("logs", "conversations", "conversations.jsonl")
        if not os.path.exists(log_path):
            return
        self.agent_response_cache.clear()
        try:
            with open(log_path, "r", encoding="utf-8") as f:
                for line in f:
                    try:
                        entry = json.loads(line)
                        if entry.get("session_id") == self.session_id:
                            state_id = entry.get("state_id", "unknown")
                            if state_id not in self.agent_response_cache:
                                self.agent_response_cache[state_id] = []
                            self.agent_response_cache[state_id].append(entry)
                    except Exception:
                        continue
        except Exception as e:
            self.logger.error(f"Failed to populate agent response cache: {e}", action="populate_agent_response_cache", layer="orchestrator_v3")

    async def get_user_query(self, retries=3) -> Optional[str]:
        for attempt in range(retries):
            clean_text = await self.memory_manager.get("clean_text")
            if clean_text:
                return clean_text
            await asyncio.sleep(0.5)
        self.logger.error("User query (clean_text) not found after retries", action="get_user_query", layer="orchestrator_v3")
        return None

    async def get_agent_responses(self, state_id: str) -> List[str]:
        # Return all agent outputs for this state from the cache
        responses = []
        for entry in self.agent_response_cache.get(state_id, []):
            output = entry.get("output")
            if output:
                if isinstance(output, dict) and "llm_answer" in output:
                    responses.append(output["llm_answer"])
                elif isinstance(output, str):
                    responses.append(output)
        return responses

    async def get_agent_confidence(self, agent_name: str) -> Optional[float]:
        # Try to get confidence from Redis (publish/subscribe or key)
        try:
            key = f"session:{self.session_id}:agent:{agent_name}:confidence"
            confidence = await self.redis_client.get(key)
            if confidence is not None:
                try:
                    return float(confidence)
                except Exception:
                    return None
            self.logger.warning(f"Confidence value missing for agent {agent_name}", action="get_agent_confidence", layer="orchestrator_v3")
        except Exception as e:
            self.logger.warning(f"Error retrieving confidence for agent {agent_name}: {e}", action="get_agent_confidence", layer="orchestrator_v3")
        return None

    async def get_state_summary(self) -> str:
        # Get workflow and pipeline state summaries
        workflow_state = await self.state_manager.getCurrentWorkflowState()
        pipeline_state = await self.state_manager.getCurrentPipelineState()
        allowed_actions = await self.state_manager.getAllowedActions()
        summary = (
            f"Workflow: {self.workflow_name}\n"
            f"Current Workflow State: {workflow_state}\n"
            f"Current Pipeline State: {pipeline_state}\n"
            f"Allowed Actions: {allowed_actions}\n"
            f"Prohibited Actions: {self.prohibited_actions}"
        )
        return summary

    async def evaluate_with_llm(self, user_query: str, agent_responses: List[str], agent_confidence: Optional[float], state_summary: str) -> str:
        prompt = (
            "You are an AI orchestrator evaluating a conversation state. Based on the following information, decide whether to PROCEED to the next state or REDO the current state.\n"
            f"State Summary: {state_summary}\n"
            f"User Query: {user_query if user_query is not None else '[Not available]'}\n"
            f"Agent Responses: {agent_responses}\n"
            f"Agent Confidence: {agent_confidence}\n"
            "If the user query is not available, make your best decision based on the other information.\n"
            "Respond with ONLY one word: 'proceed' or 'redo'."
        )
        print("[DEBUG] LLM prompt:", prompt)
        print("[DEBUG] LLM inputs:", {
            "user_query": user_query,
            "agent_responses": agent_responses,
            "agent_confidence": agent_confidence,
            "state_summary": state_summary
        })
        try:
            client = openai.AsyncOpenAI(api_key=os.getenv("OPENAI_API_KEY"))
            response = await client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=10,
                temperature=0.1
            )
            decision = response.choices[0].message.content.strip().lower()
            print("[DEBUG] LLM raw response:", response)
            return "proceed" if "proceed" in decision else "redo"
        except Exception as e:
            print(f"[DEBUG] LLM evaluation failed, defaulting to 'redo': {e}")
            self.logger.error(f"LLM evaluation failed: {e}", action="evaluate_with_llm", layer="orchestrator_v3")
            return "redo"

    async def run(self):
        await self.initialize()
        self.status = "running"
        user_query_found = False
        try:
            while True:
                # Always re-fetch the current pipeline state and pipeline at the start of each iteration
                state_summary = await self.get_state_summary()
                pipeline_state = await self.state_manager.getCurrentPipelineState()
                raw_pipeline = await self.state_manager.getCurrentPipeline()
                # Fix: get the actual list of step objects
                if hasattr(raw_pipeline, 'pipeline'):
                    print("[DEBUG] getCurrentPipeline() returned Layer2 object, using .pipeline attribute")
                    current_pipeline = raw_pipeline.pipeline
                else:
                    print("[DEBUG] getCurrentPipeline() returned list directly")
                    current_pipeline = raw_pipeline
                print("[DEBUG] type(current_pipeline):", type(current_pipeline))
                if hasattr(current_pipeline, '__len__') and len(current_pipeline) > 0:
                    print("[DEBUG] type(current_pipeline[0]):", type(current_pipeline[0]))
                    print("[DEBUG] current_pipeline[0]:", current_pipeline[0])
                print("[DEBUG] Current pipeline state:", pipeline_state)
                # Defensive: Check current_pipeline is not None and is iterable
                if current_pipeline is None:
                    self.logger.error("Current pipeline is None. Aborting.")
                    return {'status': 'aborted', 'reason': 'Current pipeline is None.'}
                if not hasattr(current_pipeline, '__iter__'):
                    self.logger.error(f"Current pipeline is not iterable: {type(current_pipeline)}. Aborting.")
                    return {'status': 'aborted', 'reason': f'Current pipeline is not iterable: {type(current_pipeline)}'}
                # Defensive: Check pipeline_state is not None
                if pipeline_state is None:
                    self.logger.error("Pipeline state is None. Aborting.")
                    return {'status': 'aborted', 'reason': 'Pipeline state is None.'}
                # Use attribute access for PipelineStep objects
                current_step = None
                for step in current_pipeline:
                    if step is None:
                        self.logger.warning("Encountered None in current_pipeline steps. Skipping.")
                        continue
                    if hasattr(step, "step") and step.step == pipeline_state:
                        current_step = step
                        break
                if not current_step:
                    self.logger.error(f"No pipeline step found for state: {pipeline_state}. Aborting.")
                    return {'status': 'aborted', 'reason': f'No pipeline step found for state: {pipeline_state}'}
                # Defensive: Check current_step.input and current_step.output are dicts
                if not isinstance(current_step.input, dict):
                    self.logger.error(f"current_step.input is not a dict: {type(current_step.input)}. Aborting.")
                    return {'status': 'aborted', 'reason': f'current_step.input is not a dict: {type(current_step.input)}'}
                if not isinstance(current_step.output, dict):
                    self.logger.error(f"current_step.output is not a dict: {type(current_step.output)}. Aborting.")
                    return {'status': 'aborted', 'reason': f'current_step.output is not a dict: {type(current_step.output)}'}
                # Get agent names (support string or list for future-proofing)
                agent_names = [current_step.agent] if isinstance(current_step.agent, str) else list(current_step.agent)
                # Defensive: Check agent_names is a list and not None
                if agent_names is None or not isinstance(agent_names, list):
                    self.logger.error(f"agent_names is not a list: {type(agent_names)}. Aborting.")
                    return {'status': 'aborted', 'reason': f'agent_names is not a list: {type(agent_names)}'}
                # Build input_data from contextual memory using step.input dict
                input_data = {}

                # Define field mapping for schema mismatches
                field_mappings = {
                    'preprocessing': {
                        'text': 'transcript'  # Pipeline config uses 'text', agent expects 'transcript'
                    },
                    'filler_tts': {
                        'fallback_message': 'filler_text'  # Pipeline config uses 'fallback_message', agent expects 'filler_text'
                    },
                    'processing': {
                        'text': 'clean_text'  # Pipeline config uses 'text', agent expects 'clean_text'
                    },
                    'tts': {
                        'greeting_response': 'llm_answer'  # Pipeline config uses 'greeting_response', agent outputs 'llm_answer'
                    }
                }

                # Define additional required fields for specific steps
                additional_fields = {
                    'tts': ['emotion', 'gender']  # TTS step requires emotion and gender fields
                }

                for input_key, mem_key in current_step.input.items():
                    value = None

                    # Strategy 1: Try to get from contextual memory directly
                    value = await self.memory_manager.get(mem_key)

                    # Strategy 2: If not found, try to get from shared context (Redis)
                    if value is None:
                        shared_context = await self.memory_manager.contextual.get_all()
                        value = shared_context.get(mem_key)

                    # Strategy 3: Try to get from previous step's process output in memory
                    if value is None:
                        # Look for process_{previous_step}_output pattern
                        all_context = await self.memory_manager.contextual.get_all()
                        for context_key, context_value in all_context.items():
                            if context_key.startswith("process_") and context_key.endswith("_output"):
                                if isinstance(context_value, dict) and mem_key in context_value:
                                    value = context_value[mem_key]
                                    print(f"[DEBUG] Found '{mem_key}' in memory key '{context_key}' for step '{current_step.step}'")
                                    break

                    # Strategy 3.5: Try memory key mapping for specific cases
                    if value is None:
                        memory_key_mappings = {
                            'greeting_response': 'llm_answer'  # TTS step expects 'greeting_response' but processing outputs 'llm_answer'
                        }
                        if mem_key in memory_key_mappings:
                            mapped_key = memory_key_mappings[mem_key]
                            value = await self.memory_manager.get(mapped_key)
                            if value is not None:
                                print(f"[DEBUG] Mapped memory key '{mem_key}' to '{mapped_key}' for step '{current_step.step}'")
                            else:
                                # Try in shared context
                                shared_context = await self.memory_manager.contextual.get_all()
                                value = shared_context.get(mapped_key)
                                if value is not None:
                                    print(f"[DEBUG] Found mapped memory key '{mapped_key}' in shared context for step '{current_step.step}'")

                    # Strategy 4: Try auto-mapping from previous step's output (fallback)
                    if value is None and 'last_step_output' in locals():
                        value = last_step_output.get(input_key) or last_step_output.get(mem_key)
                        if value is not None:
                            print(f"[DEBUG] Auto-mapped missing input '{mem_key}' for step '{current_step.step}' from previous step output.")

                    # Handle missing values with defaults for specific cases
                    if value is None:
                        # Provide default values for known optional fields
                        if current_step.step == 'filler_tts' and mem_key == 'fallback_message':
                            value = "Please wait a moment while I process your request."
                            print(f"[DEBUG] Using default fallback message for step '{current_step.step}'")
                        else:
                            print(f"[DEBUG] Missing required input '{mem_key}' for step '{current_step.step}' even after auto-mapping.")
                            return {'status': 'aborted', 'reason': f"Missing required input '{mem_key}' for step '{current_step.step}'"}

                    # Apply field mapping if needed
                    final_key = input_key
                    if current_step.step in field_mappings and input_key in field_mappings[current_step.step]:
                        final_key = field_mappings[current_step.step][input_key]
                        print(f"[DEBUG] Mapped field '{input_key}' to '{final_key}' for step '{current_step.step}'")

                    input_data[final_key] = value

                # Add additional required fields for specific steps
                if current_step.step in additional_fields:
                    for additional_field in additional_fields[current_step.step]:
                        if additional_field not in input_data:
                            # Try to get the additional field from memory
                            additional_value = await self.memory_manager.get(additional_field)
                            if additional_value is None:
                                # Try from shared context
                                shared_context = await self.memory_manager.contextual.get_all()
                                additional_value = shared_context.get(additional_field)

                            if additional_value is not None:
                                input_data[additional_field] = additional_value
                                print(f"[DEBUG] Added additional field '{additional_field}' = '{additional_value}' for step '{current_step.step}'")
                            else:
                                # Provide default values for known fields
                                if additional_field == 'emotion':
                                    input_data[additional_field] = 'neutral'
                                    print(f"[DEBUG] Using default emotion 'neutral' for step '{current_step.step}'")
                                elif additional_field == 'gender':
                                    input_data[additional_field] = 'unknown'
                                    print(f"[DEBUG] Using default gender 'unknown' for step '{current_step.step}'")
                                else:
                                    print(f"[DEBUG] Warning: Could not find value for additional field '{additional_field}' in step '{current_step.step}'")

                print(f"[DEBUG] input_data for step '{current_step.step}':", input_data)
                # Execute the pipeline step
                try:
                    result = await self.state_manager.executePipelineState(input_data)
                    # Add small delay to ensure memory writes complete before next step
                    await asyncio.sleep(0.1)
                except Exception as e:
                    print(f"[DEBUG] Exception during executePipelineState: {e}")
                    return {'status': 'aborted', 'reason': f"Exception during executePipelineState: {e}"}
                # Defensive: Check result is not None before accessing output
                if result is None:
                    self.logger.warning(f"Result from executePipelineState is None for step '{current_step.step}'. Attempting fallback.")
                    result = {}
                # Universal output handling: convert to dict if needed
                output = None
                if isinstance(result, dict):
                    output = result.get('outputs') if 'outputs' in result else result.get('output') if 'output' in result else result
                elif hasattr(result, 'dict') and callable(getattr(result, 'dict')):
                    print(f"[DEBUG] Agent output for step '{current_step.step}' is a Pydantic model, using .dict()")
                    result_dict = result.dict()
                    output = result_dict.get('outputs') if 'outputs' in result_dict else result_dict.get('output') if 'output' in result_dict else result_dict
                elif hasattr(result, 'model_dump') and callable(getattr(result, 'model_dump')):
                    print(f"[DEBUG] Agent output for step '{current_step.step}' is a Pydantic v2 model, using .model_dump()")
                    result_dict = result.model_dump()
                    output = result_dict.get('outputs') if 'outputs' in result_dict else result_dict.get('output') if 'output' in result_dict else result_dict
                else:
                    print(f"[DEBUG] Agent output for step '{current_step.step}' is of type {type(result)}, attempting to use as dict.")
                    try:
                        output = dict(result)
                    except Exception as e:
                        self.logger.error(f"Agent output for step '{current_step.step}' could not be converted to dict: {e}")
                        raise RuntimeError(f"Agent output for step '{current_step.step}' could not be converted to dict: {e}")
                if output is None:
                    self.logger.warning(f"Agent output for step '{current_step.step}' is None. Attempting to fetch from memory/context as fallback.")
                    # Try to fetch expected outputs from memory/context
                    output = {}
                    for out_key, mem_key in current_step.output.items():
                        value = await self.memory_manager.get(mem_key)
                        if value is not None:
                            output[out_key] = value
                    if not output:
                        self.logger.error(f"No output found in memory/context for step '{current_step.step}'. Aborting pipeline.")
                        raise RuntimeError(f"No output for step '{current_step.step}' in agent return or memory/context.")
                # Save output for auto-mapping in next step
                last_step_output = output

                # Also save individual output keys to memory for easier access
                if isinstance(output, dict):
                    for out_key, out_value in output.items():
                        await self.memory_manager.set("contextual", out_key, out_value)
                        print(f"[DEBUG] Saved output key '{out_key}' to contextual memory for step '{current_step.step}'")

                # Track conversation for dialog logging
                await self._track_conversation_turn(current_step.step, output)
                # Defensive: Check output is a dict or iterable before iterating/accessing
                if output is None or (not hasattr(output, '__iter__') and not isinstance(output, dict)):
                    self.logger.error(f"Output for step '{current_step.step}' is not iterable or dict. Aborting.")
                    return {'status': 'aborted', 'reason': f"Output for step '{current_step.step}' is not iterable or dict."}
                # Now safe to iterate or access output
                # Try to fetch user query (clean_text) as the very last thing before evaluation
                # Try contextual memory first, then shared context
                user_query = await self.memory_manager.get('clean_text')
                if user_query is None:
                    shared_context = await self.memory_manager.contextual.get_all()
                    user_query = shared_context.get('clean_text')

                if user_query is None:
                    print("[DEBUG] clean_text not found, skipping LLM evaluation and proceeding to next pipeline step.")
                    # Find the next pipeline step
                    next_step_id = None
                    for idx, step in enumerate(current_pipeline):
                        if hasattr(step, "step") and step.step == pipeline_state:
                            if idx + 1 < len(current_pipeline):
                                next_step_id = current_pipeline[idx + 1].step
                            break
                    # Print debug info before transition
                    current_workflow_state = await self.state_manager.getCurrentWorkflowState()
                    current_pipeline_state = await self.state_manager.getCurrentPipelineState()
                    next_workflow_state = current_workflow_state  # Workflow state doesn't change for pipeline transition
                    next_pipeline_state = next_step_id if next_step_id else None
                    print(f"[DEBUG] Transitioning: current_pipeline_state={current_pipeline_state}, current_workflow_state={current_workflow_state}, next_pipeline_state={next_pipeline_state}, next_workflow_state={next_workflow_state}")
                    if next_step_id:
                        # Debug print for pipeline transition
                        current_workflow_state = await self.state_manager.getCurrentWorkflowState()
                        current_pipeline_state = await self.state_manager.getCurrentPipelineState()
                        next_workflow_state = current_workflow_state
                        next_pipeline_state = next_step_id
                        print(f"[DEBUG] Pipeline Transition: current_pipeline_state={current_pipeline_state}, current_workflow_state={current_workflow_state}, next_pipeline_state={next_pipeline_state}, next_workflow_state={next_workflow_state}")
                        self.logger.info(f"Transitioning to next pipeline step (no user_query): {next_step_id}")
                        transitioned = await self.state_manager.transitionPipeline(next_step_id)
                    else:
                        # At the end of the pipeline, transition to the next workflow state if possible
                        workflow = await self.state_manager.getWorkflow()
                        current_workflow_state = await self.state_manager.getCurrentWorkflowState()
                        current_pipeline_state = await self.state_manager.getCurrentPipelineState()
                        state_config = workflow.workflow.states.get(current_workflow_state)
                        next_workflow_state = None
                        next_pipeline_state = None
                        if state_config and state_config.transitions:
                            # Pick the first transition's target
                            next_workflow_state = state_config.transitions[0].target
                            print(f"[DEBUG] Workflow Transition: current_pipeline_state={current_pipeline_state}, current_workflow_state={current_workflow_state}, next_pipeline_state={next_pipeline_state}, next_workflow_state={next_workflow_state}")
                            transitioned = await self.state_manager.transitionWorkflow(next_workflow_state)
                        else:
                            print(f"[DEBUG] End of pipeline: no transitions, ending workflow at state: {current_workflow_state}")
                            transitioned = False
                    pipeline_state_after = await self.state_manager.getCurrentPipelineState()
                    print(f"[DEBUG] pipeline_state after transition (no user_query): {pipeline_state_after}")
                    if not transitioned:
                        print("[DEBUG] Pipeline completed (no user_query), breaking loop.")
                        self.status = "completed"
                        self.reason = "Workflow completed successfully (no user_query)."
                        self.key_events.append(self.reason)
                        break
                    continue  # Go to next loop iteration

                # Get agent responses and confidences for all agents in the current pipeline state
                agent_responses_list = []
                agent_confidences_list = []
                for agent_name in agent_names:
                    agent_responses_list.append(await self.get_agent_responses(agent_name))
                    agent_confidences_list.append(await self.get_agent_confidence(agent_name))

                # Defensive: Check agent_responses_list and agent_confidences_list are lists
                if agent_responses_list is None or not isinstance(agent_responses_list, list):
                    self.logger.error("agent_responses_list is not a list. Aborting.")
                    return {'status': 'aborted', 'reason': 'agent_responses_list is not a list.'}
                if agent_confidences_list is None or not isinstance(agent_confidences_list, list):
                    self.logger.error("agent_confidences_list is not a list. Aborting.")
                    return {'status': 'aborted', 'reason': 'agent_confidences_list is not a list.'}
                # Defensive: Flatten lists only if they are lists of lists
                all_agent_responses = []
                for sublist in agent_responses_list:
                    if sublist is None:
                        self.logger.warning("None found in agent_responses_list. Skipping.")
                        continue
                    if isinstance(sublist, list):
                        all_agent_responses.extend(sublist)
                    else:
                        all_agent_responses.append(sublist)
                all_agent_confidences = []
                for sublist in agent_confidences_list:
                    if sublist is None:
                        self.logger.warning("None found in agent_confidences_list. Skipping.")
                        continue
                    if isinstance(sublist, list):
                        all_agent_confidences.extend(sublist)
                    else:
                        all_agent_confidences.append(sublist)

                print(f"[DEBUG] pipeline_state before LLM evaluation: {pipeline_state}")
                decision = await self.evaluate_with_llm(user_query, all_agent_responses, all_agent_confidences, state_summary)
                print(f"[DEBUG] LLM decision: {decision}")
                self.key_events.append(f"Evaluated state {pipeline_state}: {decision}")
                if decision == "proceed":
                    # Try to transition to next pipeline state
                    try:
                        # Find the next pipeline step
                        next_step_id = None
                        for idx, step in enumerate(current_pipeline):
                            if hasattr(step, "step") and step.step == pipeline_state:
                                if idx + 1 < len(current_pipeline):
                                    next_step_id = current_pipeline[idx + 1].step
                                break
                        # Print debug info before transition
                        current_workflow_state = await self.state_manager.getCurrentWorkflowState()
                        current_pipeline_state = await self.state_manager.getCurrentPipelineState()
                        next_workflow_state = current_workflow_state  # Workflow state doesn't change for pipeline transition
                        next_pipeline_state = next_step_id if next_step_id else None
                        print(f"[DEBUG] Transitioning: current_pipeline_state={current_pipeline_state}, current_workflow_state={current_workflow_state}, next_pipeline_state={next_pipeline_state}, next_workflow_state={next_workflow_state}")
                        if next_step_id:
                            # Debug print for pipeline transition
                            current_workflow_state = await self.state_manager.getCurrentWorkflowState()
                            current_pipeline_state = await self.state_manager.getCurrentPipelineState()
                            next_workflow_state = current_workflow_state
                            next_pipeline_state = next_step_id
                            print(f"[DEBUG] Pipeline Transition: current_pipeline_state={current_pipeline_state}, current_workflow_state={current_workflow_state}, next_pipeline_state={next_pipeline_state}, next_workflow_state={next_workflow_state}")
                            self.logger.info(f"Transitioning to next pipeline step: {next_step_id}")
                            transitioned = await self.state_manager.transitionPipeline(next_step_id)
                        else:
                            # At the end of the pipeline, transition to the next workflow state if possible
                            workflow = await self.state_manager.getWorkflow()
                            current_workflow_state = await self.state_manager.getCurrentWorkflowState()
                            current_pipeline_state = await self.state_manager.getCurrentPipelineState()
                            state_config = workflow.workflow.states.get(current_workflow_state)
                            next_workflow_state = None
                            next_pipeline_state = None
                            if state_config and state_config.transitions:
                                # Pick the first transition's target
                                next_workflow_state = state_config.transitions[0].target
                                print(f"[DEBUG] Workflow Transition: current_pipeline_state={current_pipeline_state}, current_workflow_state={current_workflow_state}, next_pipeline_state={next_pipeline_state}, next_workflow_state={next_workflow_state}")
                                transitioned = await self.state_manager.transitionWorkflow(next_workflow_state)
                            else:
                                print(f"[DEBUG] End of pipeline: no transitions, ending workflow at state: {current_workflow_state}")
                                transitioned = False
                        # Re-fetch pipeline_state after transition
                        pipeline_state_after = await self.state_manager.getCurrentPipelineState()
                        print(f"[DEBUG] pipeline_state after transition: {pipeline_state_after}")
                        if not transitioned:
                            print("[DEBUG] Pipeline completed, breaking loop.")
                            self.status = "completed"
                            self.reason = "Workflow completed successfully."
                            self.key_events.append(self.reason)
                            break
                    except Exception as e:
                        self.logger.error(f"Transition failed: {e}", action="run", layer="orchestrator_v3")
                        # Retry once
                        try:
                            if next_step_id:
                                transitioned = await self.state_manager.transitionPipeline(next_step_id)
                            else:
                                transitioned = await self.state_manager.transitionWorkflow(None)
                            pipeline_state_after = await self.state_manager.getCurrentPipelineState()
                            print(f"[DEBUG] pipeline_state after retry transition: {pipeline_state_after}")
                            if not transitioned:
                                print("[DEBUG] Pipeline completed after retry, breaking loop.")
                                self.status = "aborted"
                                self.reason = f"Transition failed after retry: {e}"
                                self.key_events.append(self.reason)
                                break
                        except Exception as e2:
                            print("[DEBUG] Pipeline transition failed after retry, breaking loop.")
                            self.status = "aborted"
                            self.reason = f"Transition failed after retry: {e2}"
                            self.key_events.append(self.reason)
                            break
                else:
                    print(f"[DEBUG] LLM decision was not 'proceed' (was '{decision}'), breaking loop.")
                    # LLM said to redo; abort after one redo for now
                    self.status = "aborted"
                    self.reason = "LLM evaluation requested redo; aborting."
                    self.key_events.append(self.reason)
                    break
        except Exception as e:
            self.status = "aborted"
            self.reason = f"Exception in orchestrator run: {str(e)}"
            return {"status": self.status, "reason": self.reason, "key_events": [self.reason]}
        # Reference: see scripts/test_state_manager_agents_v2.py for correct state manager usage

        # Always return a summary dict, even on success
        return {"status": self.status, "reason": self.reason, "key_events": self.key_events}

    async def _track_conversation_turn(self, step_name: str, output: dict):
        """Track conversation turns for dialog logging"""
        try:
            # Capture user message from STT step
            if step_name == "stt_process" and isinstance(output, dict):
                if "text" in output:
                    self.user_message = output["text"]
                    print(f"[DEBUG] Captured user message: {self.user_message}")

            # Capture AI response from TTS step and save conversation turn
            elif step_name == "tts" and isinstance(output, dict):
                if "greeting_response" in output:
                    self.ai_response = output["greeting_response"]
                    print(f"[DEBUG] Captured AI response: {self.ai_response}")

                    # Save the complete conversation turn
                    if self.user_message and self.ai_response:
                        # Get intent from memory
                        intent = await self.memory_manager.get("intent") or "unknown"
                        await self.memory_manager.save_conversation_turn(
                            user_message=self.user_message,
                            ai_message=self.ai_response,
                            intent=intent
                        )
                        print(f"[DEBUG] Saved conversation turn: user='{self.user_message}' ai='{self.ai_response}' intent='{intent}'")

                        # Reset for next turn
                        self.user_message = ""
                        self.ai_response = ""
        except Exception as e:
            print(f"[DEBUG] Error tracking conversation turn: {e}")

    async def cleanup(self):
        # Stub for orchestrator cleanup logic
        pass