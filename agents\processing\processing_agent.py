from agents.base.base_agent import BaseAgent
from schemas.outputSchema import StateOutput, StatusType, StatusCode
from schemas.a2a_message import A2AMessage, MessageType
from core.memory.memory_manager import MemoryManager
import asyncio
import os
import openai
import json
from tenacity import retry, stop_after_attempt, wait_fixed, retry_if_exception_type
from utils.audio_utils import synthesize_fallback_audio
from utils.llm_call_tracker import track_llm_call

class ProcessingAgent(BaseAgent):
    def __init__(self, session_id=None, state_id=None):
        super().__init__("processing_agent", session_id, state_id)
        self.memory_manager = MemoryManager(session_id)
        openai_api_key = os.getenv("OPENAI_API_KEY")
        if not openai_api_key:
            raise ValueError("OPENAI_API_KEY environment variable is required")
        self.openai_client = openai.AsyncOpenAI(api_key=openai_api_key)

    def load_config(self):
        config_path = os.path.join(os.path.dirname(__file__), '../../..', 'config', 'state_manager_config.json')
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception:
            return {"enable_emotion": True, "enable_gender": True, "enable_disambiguation": False}

    async def process(self, input_data, context=None):
        start_time = asyncio.get_event_loop().time()
        context = context or {}
        self._log_process_start(input_data, context)
        session_id = self.session_id or context.get("session_id")
        redis_key = session_id
        config = self.load_config()
        try:
            # 1. Load the shared context dict (from Redis)
            shared_context = await self.load_context(redis_key) or {}
            fallback_result = await self.handle_redis_fallback(shared_context, session_id)
            if fallback_result:
                return fallback_result
            if isinstance(shared_context, dict) and "tts_response" in shared_context:
                return StateOutput(
                    status=StatusType.ERROR,
                    message="Redis unavailable. Fallback triggered.",
                    code=StatusCode.SERVICE_UNAVAILABLE,
                    outputs={"fallback_message": shared_context["tts_response"]},
                    meta={"agent": self.agent_name}
                )
            # 2. Get the preprocessed text
            clean_text = shared_context.get("clean_text")
            if not clean_text:
                return StateOutput(
                    status=StatusType.ERROR,
                    message="No clean_text found in shared context",
                    code=StatusCode.NOT_FOUND,
                    outputs={},
                    meta={"agent": self.agent_name}
                )
            # 3. Access contextual and persistent memory 
            contextual = await self.memory_manager.get_conversation()
            persistent = await self.memory_manager.get_all_persistent()
            # 4. Dummy routing logic (simulate internal, DB, or RAG)
            if "account" in clean_text:
                route_result = await self.route_internal_logic(clean_text, contextual, persistent)
            elif "database" in clean_text:
                route_result = await self.route_database(clean_text, contextual, persistent)
            elif "knowledge" in clean_text or "rag" in clean_text:
                route_result = await self.route_rag_engine(clean_text, contextual, persistent)
            else:
                route_result = await self.route_internal_logic(clean_text, contextual, persistent)
            # 5. Publish notification
            llm_answer = await self.llm_postprocess(route_result, contextual, persistent)
            # Generate business logic outputs based on intent
            intent = shared_context.get("intent", "unknown")
            business_outputs = await self.generate_business_outputs(intent, clean_text, route_result)

            result = {
                "route": route_result.get("route"),
                "llm_answer": llm_answer,
                **business_outputs
            }
            # Save user and AI turn to contextual memory for dialog history
            await self.save_conversation_turn(clean_text, llm_answer, shared_context.get("intent"))
            # RELOAD the latest context from Redis to get the updated conversation
            shared_context = await self.load_context(redis_key) or {}
            if not isinstance(shared_context, dict):
                print("[ERROR] shared_context is not a dict! Reinitializing as dict.")
                shared_context = {}
            print("[DEBUG] shared_context type before update:", type(shared_context))
            print("[DEBUG] result type:", type(result))
            shared_context.update(result)
            duration_ms = int((asyncio.get_event_loop().time() - start_time) * 1000)
            shared_context["latencyProcessing"] = duration_ms
            # Final type check before saving
            if not isinstance(shared_context, dict):
                print("[ERROR] Attempted to save non-dict context! Forcing dict.")
                shared_context = dict(shared_context)
            await self.save_context(redis_key, shared_context)
            # 7. Publish notification
            notification = A2AMessage(
                session_id=session_id,
                message_type=MessageType.NOTIFICATION,
                source_agent=self.agent_name,
                target_agent="Orchestrator",
                payload={"status": "complete"},
                context_keys_updated=list(result.keys()) + ["latencyProcessing"]
            )
            # await self.publish_notification("agent_completion", notification.to_json())
            # 8. Return result
            result["latencyProcessing"] = duration_ms
            result = StateOutput(
                status=StatusType.SUCCESS,
                message="Processing complete",
                code=StatusCode.OK,
                outputs=result,
                meta={
                    "agent": self.agent_name,
                    "latencyProcessing": duration_ms,
                    "contextual_length": len(contextual),
                    "persistent_keys": list(persistent.keys())
                }
            )
            self._log_process_end(result, duration_ms)
            return result
        except Exception as e:
            duration_ms = int((asyncio.get_event_loop().time() - start_time) * 1000)
            self._log_error(e, input_data)
            # Notify orchestrator of error
            notification = A2AMessage(
                session_id=session_id,
                message_type=MessageType.NOTIFICATION,
                source_agent=self.agent_name,
                target_agent="Orchestrator",
                payload={"status": "error", "error_message": str(e)},
                context_keys_updated=[]
            )
            # await self.publish_notification("agent_completion", notification.to_json())
            # Generate fallback audio
            fallback_text = "Sorry, something went wrong. Can you repeat that please?"
            audio_path = synthesize_fallback_audio(fallback_text, session_id=session_id or "fallback")
            outputs = {"fallback_message": fallback_text}
            if audio_path:
                outputs["audio_path"] = audio_path
            return StateOutput(
                status=StatusType.ERROR,
                message=f"ProcessingAgent error: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs=outputs,
                meta={
                    "agent": self.agent_name,
                    "latencyProcessing": duration_ms,
                    "error": str(e)
                }
            )
        
        # TODO: Implement actual internal logic here
    async def route_internal_logic(self, clean_text, contextual, persistent):
        # Dummy internal logic
        return {"route": "internal_logic", "data": f"Account info for: {clean_text}"}

        # TODO: Implement actual database logic here
    async def route_database(self, clean_text, contextual, persistent):
        # Dummy DB logic
        return {"route": "database", "data": f"Fetched from DB: {clean_text}"}

        # TODO: Implement actual RAG engine logic here
    async def route_rag_engine(self, clean_text, contextual, persistent):
        # Dummy RAG logic
        return {"route": "rag_engine", "data": f"RAG answer for: {clean_text}"}

    async def generate_business_outputs(self, intent, clean_text, route_result):
        """Generate business logic outputs based on intent and context."""
        outputs = {}

        # Generate placeholder outputs based on intent
        if intent == "check_balance" or "balance" in clean_text.lower():
            outputs["account_balance"] = "$2,547.83"
        else:
            outputs["account_balance"] = None

        if intent == "loan_inquiry" or "loan" in clean_text.lower():
            outputs["loan_eligibility"] = "Pre-approved for up to $50,000"
        else:
            outputs["loan_eligibility"] = None

        # Always provide exit signal for workflow completion
        if intent in ["goodbye", "exit", "end"] or any(word in clean_text.lower() for word in ["goodbye", "bye", "thanks", "thank you"]):
            outputs["exit_signal"] = "conversation_complete"
        else:
            outputs["exit_signal"] = "continue"

        return outputs

    @retry(stop=stop_after_attempt(3), wait=wait_fixed(1), retry=retry_if_exception_type(Exception))
    async def llm_postprocess(self, route_result, contextual, persistent):
        prompt = (
            "You are an AI assistant. Given the following routed result, contextual memory, and persistent memory, "
            "generate a helpful, user-facing answer.\n"
            f"Routed Result: {route_result}\n"
            f"Contextual Memory: {contextual}\n"
            f"Persistent Memory: {persistent}\n"
            "Answer:"
        )
        try:
            response = await self.openai_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=128,
                temperature=0.3
            )
            result = response.choices[0].message.content.strip()

            # Track LLM call for token usage
            asyncio.create_task(track_llm_call(self.memory_manager, "gpt-4o-mini", prompt, response, "postprocess"))

            return result
        except Exception as e:
            self.logger.warning(f"LLM postprocess failed: {e}")
            return route_result.get("data", "No answer available.") 