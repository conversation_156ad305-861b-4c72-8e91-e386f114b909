{"id": "l2_loan_application", "version": "1.0", "pipeline": [{"step": "stt", "process": "stt_process", "agent": "stt_agent", "input": {"audio": "audio_path"}, "tools": {"external_tools": "stt"}, "output": {"text": "text", "latencySTT": "latencySTT", "audio_path": "audio_path"}}, {"step": "preprocessing", "process": "preprocessing_process", "agent": "preprocessing_agent", "input": {"text": "text"}, "tools": {"external_tools": "openai"}, "output": {"income": "income", "credit_score": "credit_score", "requested_amount": "requested_amount", "loan_type": "loan_type", "latencyPreprocessing": "latencyPreprocessing", "fallback_message": "fallback_message"}}, {"step": "filler_tts", "process": "filler_tts_process", "agent": "filler_tts_agent", "input": {"fallback_message": "fallback_message"}, "tools": {"external_tools": "openai"}, "output": {"audio_path": "audio_path", "latencyFillerTTS": "latencyFillerTTS"}}, {"step": "processing", "process": "processing_process", "agent": "processing_agent", "input": {"income": "income", "credit_score": "credit_score", "requested_amount": "requested_amount", "loan_type": "loan_type"}, "tools": {"external_tools": "credit_check_system"}, "output": {"loan_approval_status": "loan_approval_status", "interest_rate": "interest_rate", "monthly_payment": "monthly_payment", "latencyProcessing": "latencyProcessing"}}, {"step": "tts", "process": "tts_process", "agent": "tts_agent", "input": {"text": "loan_approval_status"}, "tools": {"external_tools": "openai"}, "output": {"audio_path": "audio_path", "latencyTTS": "latencyTTS"}}], "onInterrupt": {"handler": "interrupt_manager", "resume_from": "stt"}, "onError": {"retry": 3, "fallback_state": "l2_fallback_loan"}, "outputs": ["confirmation_message"]}