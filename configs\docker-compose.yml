version: '3.8'

services:
  # Redis service
  redis:
    image: redis/redis-stack-server:latest
    container_name: voice_agents_redis
    ports:
      - "6379:6379"  # Redis server
      - "8001:8001"  
    volumes:
      - redis_data:/data
    restart: unless-stopped
  mongodb:
    image: mongo:6.0
    container_name: mongodb
    restart: always
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGODB_USERNAME}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGODB_PASSWORD}
    volumes:
      - mongo_data:/data/db

  # Qdrant vector database
  qdrant:
    image: qdrant/qdrant:latest
    container_name: voice_agents_qdrant
    ports:
      - "6333:6333"  # REST API
      - "6334:6334"  # GRPC API
    volumes:
      - qdrant_data:/qdrant/storage
    restart: unless-stopped
    environment:
      - QDRANT__SERVICE__HTTP_PORT=6333
      - QDRANT__SERVICE__GRPC_PORT=6334

  # Main application
  app:
    build: .
    container_name: voice_agents_app
    ports:
      - "8000:8000"
    volumes:
      - ./logs:/app/logs
      - ./config:/app/config
    depends_on:
      - redis
      - qdrant
    #restart: unless-stopped
    env_file:
      - .env
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - QDRANT_HOST=qdrant
      - QDRANT_PORT=6333
      - PYTHONPATH=/app

volumes:
  redis_data:
  qdrant_data:
  mongo_data:
