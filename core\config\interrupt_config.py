"""
Interrupt Handling Configuration System

This module provides configurable settings for interrupt sensitivity,
confirmation requirements, and user preferences for the TTS interrupt system.
"""

import os
import json
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict
from pathlib import Path
from dotenv import load_dotenv

from core.logging.logger_config import get_module_logger

# Load environment variables
load_dotenv()

logger = get_module_logger("interrupt_config")


@dataclass
class GlobalInterruptSettings:
    """Global interrupt settings configuration."""
    enabled: bool = True
    vad_threshold: float = float(os.getenv('VAD_THRESHOLD', '0.05'))
    confirmation_window_seconds: float = 0.5
    min_interrupt_duration_seconds: float = 0.3
    tts_interrupt_cooldown_seconds: float = float(os.getenv('TTS_INTERRUPT_COOLDOWN_SECONDS', '0.0'))


@dataclass
class InterruptConfig:
    """Simplified interrupt configuration with only global settings."""
    global_settings: GlobalInterruptSettings

    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        return asdict(self)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'InterruptConfig':
        """Create configuration from dictionary."""
        interrupt_config_data = data.get('interrupt_config', data)
        return cls(
            global_settings=GlobalInterruptSettings(**interrupt_config_data.get('global_settings', {}))
        )

    # Backward compatibility properties
    @property
    def detection(self):
        """Backward compatibility property."""
        return self.global_settings


class InterruptConfigManager:
    """
    Manages interrupt configuration loading and saving.
    (Per-user config logic removed.)
    """
    
    def __init__(self, config_dir: Optional[str] = None):
        self.config_dir = Path(config_dir) if config_dir else Path(__file__).parent
        self.default_config_file = self.config_dir / "interrupt_config_default.json"
        self.logger = logger
        self._default_config = None

    def get_default_config(self) -> InterruptConfig:
        """Get the default interrupt configuration."""
        if self._default_config is None:
            self._default_config = self._load_default_config()
        return self._default_config

    def _load_default_config(self) -> InterruptConfig:
        """Load default configuration from file or create default, with environment variable overrides."""
        try:
            # Always create configuration with environment variable overrides
            config = InterruptConfig(
                global_settings=GlobalInterruptSettings()
            )

            if self.default_config_file.exists():
                self.logger.info(
                    "Default configuration created with environment variable overrides",
                    action="_load_default_config",
                    output_data={
                        "config_file": str(self.default_config_file),
                        "vad_threshold": config.global_settings.vad_threshold,
                        "tts_cooldown": config.global_settings.tts_interrupt_cooldown_seconds
                    },
                    layer="interrupt_config"
                )
            else:
                # Save default configuration
                self._save_default_config(config)
                self.logger.info(
                    "Default configuration created with environment variable overrides",
                    action="_load_default_config",
                    output_data={
                        "vad_threshold": config.global_settings.vad_threshold,
                        "tts_cooldown": config.global_settings.tts_interrupt_cooldown_seconds
                    },
                    layer="interrupt_config"
                )

            return config
        except Exception as e:
            self.logger.error(
                "Error loading default configuration",
                action="_load_default_config",
                reason=str(e),
                layer="interrupt_config"
            )
            # Return configuration with environment variable overrides as fallback
            return InterruptConfig(
                global_settings=GlobalInterruptSettings()
            )

    def _save_default_config(self, config: InterruptConfig):
        """Save default configuration to file."""
        try:
            with open(self.default_config_file, 'w') as f:
                json.dump(config.to_dict(), f, indent=2)
            self.logger.info(
                "Default configuration saved",
                action="_save_default_config",
                output_data={"config_file": str(self.default_config_file)},
                layer="interrupt_config"
            )
        except Exception as e:
            self.logger.error(
                "Error saving default configuration",
                action="_save_default_config",
                reason=str(e),
                layer="interrupt_config"
            )

# Global configuration manager instance
_config_manager = None

def get_config_manager() -> InterruptConfigManager:
    """Get the global configuration manager instance."""
    global _config_manager
    if _config_manager is None:
        _config_manager = InterruptConfigManager()
    return _config_manager

def get_interrupt_config(workflow_config: Optional[Dict[str, Any]] = None) -> InterruptConfig:
    """
    Get interrupt configuration with optional workflow override.

    Args:
        workflow_config: Optional workflow configuration dict to merge with defaults

    Returns:
        InterruptConfig: Merged configuration with workflow overrides applied
    """
    # Start with default configuration
    default_config = get_config_manager().get_default_config()

    # If no workflow config provided, return default
    if not workflow_config:
        return default_config

    # Extract workflow interrupt config
    workflow_interrupt_config = workflow_config.get('workflow', {}).get('interrupt_config', {})
    if not workflow_interrupt_config:
        return default_config

    # Merge workflow settings with defaults
    merged_settings = {
        'enabled': workflow_interrupt_config.get('global_settings', {}).get('enabled', default_config.global_settings.enabled),
        'vad_threshold': workflow_interrupt_config.get('global_settings', {}).get('vad_threshold', default_config.global_settings.vad_threshold),
        'confirmation_window_seconds': workflow_interrupt_config.get('global_settings', {}).get('confirmation_window_seconds', default_config.global_settings.confirmation_window_seconds),
        'min_interrupt_duration_seconds': workflow_interrupt_config.get('global_settings', {}).get('min_interrupt_duration_seconds', default_config.global_settings.min_interrupt_duration_seconds),
        'tts_interrupt_cooldown_seconds': workflow_interrupt_config.get('global_settings', {}).get('tts_interrupt_cooldown_seconds', default_config.global_settings.tts_interrupt_cooldown_seconds)
    }

    # Create new config with merged settings
    return InterruptConfig(
        global_settings=GlobalInterruptSettings(**merged_settings)
    )
