"""
Interrupt Management System

This module handles all interrupt-related functionality for the voice agent platform,
including interrupt detection, context management, TTS pause/resume, and queued input processing.
"""

import asyncio
import time
from typing import Dict, Any, Optional, List
from datetime import datetime

from core.logging.logger_config import get_module_logger
from schemas.outputSchema import StateOutput, StatusType, StatusCode
from core.interruption.action_reversibility import ActionReversibilityDetector, detect_action_reversibility, get_interrupt_message
from core.state_manager.state_output import InterruptState
from core.config.interrupt_config import get_interrupt_config


class InterruptManager:
    """
    Manages all interrupt-related functionality for the StateManager.
    Handles interrupt detection, context storage, TTS control, and queued input processing.
    """
    
    def __init__(self, session_id: str, memory_manager, interrupt_config=None):
        self.session_id = session_id
        self.memory_manager = memory_manager
        self.interrupt_config = interrupt_config
        self.logger = get_module_logger("InterruptManager", session_id=session_id)
        
        # Interrupt state tracking
        self.interrupt_in_progress = False
        self.current_tts_playback = None
        
        # Action reversibility detector
        self.reversibility_detector = ActionReversibilityDetector()
    
    async def handle_interrupt_event(self, interrupt_data: Dict[str, Any]) -> StateOutput:
        """
        Handle an interrupt event with comprehensive context management.
        
        Args:
            interrupt_data: Dictionary containing interrupt information
            
        Returns:
            StateOutput with interrupt handling results
        """
        try:
            self.logger.info(
                "Handling interrupt event",
                action="handle_interrupt_event",
                input_data=interrupt_data,
                layer="interrupt_manager"
            )
            
            # Extract interrupt details
            user_input = interrupt_data.get("user_input", "[User interrupted during TTS]")
            playback_position = interrupt_data.get("playback_position", 0.0)
            audio_path = interrupt_data.get("audio_path")
            action_reversible = interrupt_data.get("action_reversible", True)
            
            # Store interrupt context in memory
            await self.memory_manager.set_interrupt_context(
                detected=True,
                confirmed=interrupt_data.get("confirmed", True),
                user_input_queued=user_input,
                resume_after_acknowledgment=interrupt_data.get("resume_after_acknowledgment", True),
                action_reversible=action_reversible,
                interrupt_timestamp=interrupt_data.get("timestamp", datetime.now().isoformat()),
                handled=False
            )
            
            # Update TTS playback state if audio path provided
            if audio_path:
                await self.memory_manager.set_tts_playback_state(
                    audio_path=audio_path,
                    status="interrupted",
                    playback_position=playback_position
                )
            
            # Set interrupt flags for processing
            if user_input and user_input not in ["[Interrupt detected]", "[User interrupted during TTS]"]:
                if action_reversible:
                    self.logger.info(
                        "Setting queue flags for reversible action",
                        action="handle_interrupt_event",
                        output_data={"user_input_queued": user_input},
                        layer="interrupt_manager"
                    )
                    await self.memory_manager.set("contextual", "queued_user_input", user_input)
                    await self.memory_manager.set("contextual", "process_queued_after_tts", True)
                else:
                    self.logger.info(
                        "No queuing for irreversible action",
                        action="handle_interrupt_event",
                        output_data={"user_input": user_input},
                        layer="interrupt_manager"
                    )
            
            self.interrupt_in_progress = True
            
            return StateOutput(
                status=StatusType.SUCCESS,
                message="Interrupt event handled successfully",
                code=StatusCode.OK,
                outputs={
                    "interrupt_handled": True,
                    "user_input_queued": user_input,
                    "action_reversible": action_reversible,
                    "playback_position": playback_position
                },
                meta={"interrupt_manager": "event_handled"}
            )
            
        except Exception as e:
            self.logger.error(
                "Error handling interrupt event",
                action="handle_interrupt_event",
                reason=str(e),
                layer="interrupt_manager"
            )
            
            return StateOutput(
                status=StatusType.ERROR,
                message=f"Failed to handle interrupt event: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"error": str(e)}
            )
    
    async def monitor_and_handle_interrupts(self) -> None:
        """
        Monitor for pending interrupts and handle them appropriately.
        This method checks for unhandled interrupts in memory and processes them.
        """
        try:
            # Check for pending interrupt context
            interrupt_context = await self.memory_manager.get_interrupt_context()
            
            if not interrupt_context or interrupt_context.get("handled", False):
                return  # No pending interrupts
            
            # Look for the latest interrupt that needs handling
            interrupts = await self.memory_manager.get("interrupts") or []
            latest_interrupt = None
            
            for interrupt in reversed(interrupts):  # Check most recent first
                if interrupt.get("requires_handling", False) and not interrupt.get("handled", False):
                    latest_interrupt = interrupt
                    break
            
            if latest_interrupt:
                # Mark as being handled to prevent duplicate processing
                interrupt_context["handled"] = True
                await self.memory_manager.set_interrupt_context(**interrupt_context)
                
                # Handle the interrupt using the complete data
                await self.handle_interrupt_event(latest_interrupt)
            else:
                self.logger.warning(
                    "Interrupt context exists but no unhandled interrupt found",
                    action="monitor_and_handle_interrupts",
                    layer="interrupt_manager"
                )
                
        except Exception as e:
            self.logger.error(
                "Error monitoring and handling interrupts",
                action="monitor_and_handle_interrupts",
                reason=str(e),
                layer="interrupt_manager"
            )
    
    async def process_queued_input_after_tts(self, state_manager) -> bool:
        """
        Process queued user input after TTS completion.
        
        Args:
            state_manager: Reference to the StateManager for executing new workflows
            
        Returns:
            bool: True if queued input was processed, False otherwise
        """
        try:
            # Check if there's queued input to process
            should_process = await self.memory_manager.get("process_queued_after_tts")
            queued_input = await self.memory_manager.get("queued_user_input")
            
            if should_process and queued_input:
                self.logger.info(
                    "Processing queued user input after TTS",
                    action="process_queued_input_after_tts",
                    input_data={"queued_input": queued_input},
                    layer="interrupt_manager"
                )

                # Check if queued input is a placeholder - if so, skip processing
                if queued_input.startswith("[") and queued_input.endswith("]"):
                    self.logger.warning(
                        f"Skipping placeholder queued input: {queued_input}",
                        action="process_queued_input_after_tts",
                        layer="interrupt_manager"
                    )
                    # Clear the flags
                    await self.memory_manager.set("contextual", "process_queued_after_tts", False)
                    await self.memory_manager.set("contextual", "queued_user_input", None)
                    return False

                # Execute the complete StateManager workflow with context enhancement
                try:
                    await self._execute_enhanced_queued_input_workflow(state_manager, queued_input)

                    # Clear the processing flags
                    await self.memory_manager.set("contextual", "process_queued_after_tts", False)
                    await self.memory_manager.set("contextual", "queued_user_input", None)

                    return True

                except Exception as e:
                    self.logger.error(
                        f"Error executing enhanced workflow for queued input: {e}",
                        action="process_queued_input_after_tts",
                        layer="interrupt_manager"
                    )
                    return False
            
            return False

        except Exception as e:
            self.logger.error(
                "Error processing queued input after TTS",
                action="process_queued_input_after_tts",
                reason=str(e),
                layer="interrupt_manager"
            )

    async def _execute_enhanced_queued_input_workflow(self, state_manager, queued_input: str):
        """
        Execute a complete StateManager workflow for queued user input with context enhancement.
        This method enhances the queued input with previous context and processes it through
        the complete pipeline workflow (not just preprocessing).

        Args:
            state_manager: Reference to the StateManager
            queued_input: The user input to process
        """
        try:
            self.logger.info(
                "Executing enhanced workflow for queued input",
                action="_execute_enhanced_queued_input_workflow",
                input_data={"queued_input": queued_input},
                layer="interrupt_manager"
            )

            # Step 1: Retrieve context from previous interaction
            last_user_message = ""
            last_user_intent = "unknown"
            if self.memory_manager:
                # Get from contextual memory where these values are stored by PreProcessingState
                last_user_message = await self.memory_manager.get("last_user_message") or ""
                last_user_intent = await self.memory_manager.get("last_user_intent") or "unknown"
                self.logger.info(
                    "Retrieved context from previous interaction",
                    action="_execute_enhanced_queued_input_workflow",
                    output_data={
                        "last_user_message": last_user_message,
                        "last_user_intent": last_user_intent
                    },
                    layer="interrupt_manager"
                )

            # Step 2: Enhance queued input with context
            if last_user_message and last_user_message.strip() and last_user_intent != "unknown":
                enhanced_input = (
                    f"User previously asked: {last_user_message.strip()} (intent: {last_user_intent}). "
                    f"Then clarified: {queued_input.strip()}"
                )
                self.logger.info(
                    "Enhanced queued input with previous context",
                    action="_execute_enhanced_queued_input_workflow",
                    input_data={
                        "original_input": queued_input,
                        "enhanced_input": enhanced_input
                    },
                    layer="interrupt_manager"
                )
            else:
                enhanced_input = queued_input
                self.logger.info(
                    "No previous context available, using original input",
                    action="_execute_enhanced_queued_input_workflow",
                    input_data={"enhanced_input": enhanced_input},
                    layer="interrupt_manager"
                )

            # Step 3: Execute complete StateManager workflow
            await self._execute_complete_workflow_for_queued_input(state_manager, enhanced_input)

        except Exception as e:
            self.logger.error(
                "Error executing enhanced queued input workflow",
                action="_execute_enhanced_queued_input_workflow",
                reason=str(e),
                layer="interrupt_manager"
            )

    async def _execute_complete_workflow_for_queued_input(self, state_manager, enhanced_input: str):
        """
        Execute the complete StateManager workflow for queued input.
        This simulates a new user input going through the complete pipeline:
        STT (skipped, we have transcript) -> Preprocessing -> Processing -> TTS

        Args:
            state_manager: Reference to the StateManager
            enhanced_input: The enhanced user input with context
        """
        try:
            self.logger.info(
                "Starting complete workflow execution for queued input",
                action="_execute_complete_workflow_for_queued_input",
                input_data={"enhanced_input": enhanced_input},
                layer="interrupt_manager"
            )

            # Step 1: Preprocessing to extract intent
            await state_manager.transitionPipeline("preprocessing")
            self.logger.info("Step 1: Preprocessing - Extracting intent...")

            preprocessing_result = await state_manager.executePipelineState({"transcript": enhanced_input})
            self.logger.info(f"Preprocessing result status: {preprocessing_result.status.value}")

            if preprocessing_result.status.value != "success":
                self.logger.error(
                    f"Preprocessing failed for queued input: {preprocessing_result.message}",
                    action="_execute_complete_workflow_for_queued_input",
                    layer="interrupt_manager"
                )
                return

            # Extract preprocessing outputs
            intent = preprocessing_result.outputs.get("intent", "unknown")
            clean_text = preprocessing_result.outputs.get("clean_text", enhanced_input)
            emotion = preprocessing_result.outputs.get("emotion", "neutral")
            gender = preprocessing_result.outputs.get("gender", "neutral")

            self.logger.info(
                "Preprocessing completed successfully",
                action="_execute_complete_workflow_for_queued_input",
                output_data={
                    "intent": intent,
                    "clean_text": clean_text,
                    "emotion": emotion,
                    "gender": gender
                },
                layer="interrupt_manager"
            )

            # Step 2: Determine target state based on intent (if StateManager supports it)
            if hasattr(state_manager, '_get_target_state_for_intent'):
                target_state = state_manager._get_target_state_for_intent(intent)
                if target_state and target_state != state_manager.current_workflow_state_id:
                    self.logger.info(f"Step 2: Transitioning to target state: {target_state}")
                    await state_manager.transitionWorkflow(target_state)

            # Step 3: Execute processing
            await state_manager.transitionPipeline("processing")
            self.logger.info("Step 3: Processing - Executing processing state...")

            processing_result = await state_manager.executePipelineState({
                "clean_text": clean_text,
                "intent": intent,
                "emotion": emotion,
                "gender": gender
            })

            if processing_result.status.value != "success":
                self.logger.error(
                    f"Processing failed for queued input: {processing_result.message}",
                    action="_execute_complete_workflow_for_queued_input",
                    layer="interrupt_manager"
                )
                return

            # Extract processing outputs
            ai_response = processing_result.outputs.get("llm_answer", "I'm sorry, I couldn't process your request.")

            self.logger.info(
                "Processing completed successfully",
                action="_execute_complete_workflow_for_queued_input",
                output_data={"ai_response": ai_response},
                layer="interrupt_manager"
            )

            # Step 4: Execute TTS
            await state_manager.transitionPipeline("tts")
            self.logger.info("Step 4: TTS - Generating response audio...")

            # Execute TTS with normal interrupt monitoring enabled for queued input
            # This ensures audio playback happens and new interrupts can be detected
            tts_result = await state_manager.executePipelineState({
                "text": ai_response,
                "emotion": emotion,
                "gender": gender
            })

            if tts_result.status.value == "success":
                self.logger.info(
                    "Complete workflow executed successfully for queued input",
                    action="_execute_complete_workflow_for_queued_input",
                    output_data={"tts_audio_path": tts_result.outputs.get("audio_path")},
                    layer="interrupt_manager"
                )

                # The StateManager will automatically handle TTS playback and interrupt monitoring
                # since skip_interrupt_monitoring is not set to True

            else:
                self.logger.error(
                    f"TTS failed for queued input: {tts_result.message}",
                    action="_execute_complete_workflow_for_queued_input",
                    layer="interrupt_manager"
                )

        except Exception as e:
            self.logger.error(
                "Error executing complete workflow for queued input",
                action="_execute_complete_workflow_for_queued_input",
                reason=str(e),
                layer="interrupt_manager"
            )
            return False
    
    async def start_tts_interrupt_monitoring(self, tts_result: StateOutput) -> None:
        """
        Start interrupt monitoring for TTS playback.
        
        Args:
            tts_result: Result from TTS execution containing audio path
        """
        try:
            audio_path = tts_result.outputs.get("audio_path")
            if not audio_path:
                self.logger.warning("No audio path provided for interrupt monitoring")
                return
            
            # Store TTS playback state for potential interruption
            await self.memory_manager.set_tts_playback_state(
                audio_path=audio_path,
                status="playing",
                playback_position=0.0,
                message_hash=None
            )
            
            self.current_tts_playback = {
                "audio_path": audio_path,
                "start_time": time.time(),
                "status": "playing"
            }
            
            self.logger.info(
                "Started TTS interrupt monitoring",
                action="start_tts_interrupt_monitoring",
                output_data={"audio_path": audio_path},
                layer="interrupt_manager"
            )
            
        except Exception as e:
            self.logger.error(
                "Error starting TTS interrupt monitoring",
                action="start_tts_interrupt_monitoring",
                reason=str(e),
                layer="interrupt_manager"
            )
    
    async def _execute_queued_input_workflow(self, state_manager, queued_input: str):
        """
        Execute a complete workflow for queued user input.
        This simulates the user providing new input after interrupt completion.

        Args:
            state_manager: Reference to the StateManager
            queued_input: The user input to process
        """
        try:
            self.logger.info(
                "Executing workflow for queued input",
                action="_execute_queued_input_workflow",
                input_data={"queued_input": queued_input},
                layer="interrupt_manager"
            )

            # Execute the complete pipeline flow for the queued input
            # This delegates back to the StateManager's pipeline execution
            if hasattr(state_manager, 'execute_complete_pipeline_flow'):
                await state_manager.execute_complete_pipeline_flow(queued_input)
            elif hasattr(state_manager, '_execute_queued_input_workflow'):
                await state_manager._execute_queued_input_workflow(queued_input)
            else:
                self.logger.warning(
                    "No suitable workflow execution method found in StateManager",
                    action="_execute_queued_input_workflow",
                    layer="interrupt_manager"
                )

        except Exception as e:
            self.logger.error(
                "Error executing queued input workflow",
                action="_execute_queued_input_workflow",
                reason=str(e),
                layer="interrupt_manager"
            )

    async def handle_interrupt(self, audio_data: bytes,
                             current_tts_audio_path: Optional[str] = None,
                             playback_position: Optional[float] = None,
                             user_input: Optional[str] = None,
                             state_manager=None) -> StateOutput:
        """
        Handle an interrupt event during workflow execution.

        Simple approach: Create InterruptState and process the interrupt directly.
        """
        try:
            if self.interrupt_in_progress:
                self.logger.warning(
                    "Interrupt already in progress",
                    action="handle_interrupt",
                    layer="interrupt_manager"
                )
                return StateOutput(
                    status=StatusType.SUCCESS,
                    message="Interrupt already in progress",
                    code=StatusCode.OK,
                    outputs={"interrupt_handled": False},
                    meta={"reason": "interrupt_already_in_progress"}
                )

            self.interrupt_in_progress = True

            # Create InterruptState instance
            interrupt_state = InterruptState(
                state_id="interrupt_handler",
                agent_registry=state_manager.agent_registry if state_manager else None,
                session_id=self.session_id,
                interrupt_config=self.interrupt_config,
                state_manager=state_manager
            )

            # Prepare interrupt input
            interrupt_input = {
                "audio_data": audio_data,
                "current_tts_audio_path": current_tts_audio_path or self.current_tts_playback,
                "playback_position": playback_position or 0.0,
                "user_input": user_input or "[Interrupt detected]"
            }

            # Get workflow state configuration if state_manager is available
            interrupt_config_dict = None
            reversible = None
            if state_manager and hasattr(state_manager, 'raw_workflow_dict') and hasattr(state_manager, 'current_workflow_state_id'):
                try:
                    raw_state_dict = state_manager.raw_workflow_dict['workflow']['states'][state_manager.current_workflow_state_id]
                    interrupt_config_dict = raw_state_dict.get('interrupt_config', None)
                    reversible = interrupt_config_dict.get('reversible') if interrupt_config_dict else None
                except (KeyError, AttributeError) as e:
                    self.logger.warning(f"Could not extract workflow state config: {e}")

            context = {
                "workflow_state_config": {
                    "interrupt_config": interrupt_config_dict,
                    "reversible": reversible,
                },
                "memory_manager": self.memory_manager,
                "session_id": self.session_id,
                "user_id": getattr(state_manager, 'user_id', None) if state_manager else None
            }

            # Process interrupt
            result = await interrupt_state.process(interrupt_input, context)

            self.interrupt_in_progress = False
            return result

        except Exception as e:
            self.interrupt_in_progress = False
            self.logger.error(
                "Error handling interrupt",
                action="handle_interrupt",
                reason=str(e),
                layer="interrupt_manager"
            )
            return StateOutput(
                status=StatusType.ERROR,
                message=f"Interrupt handling error: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"error": str(e)}
            )

    async def handle_detected_interrupt(self, audio_data: bytes, current_tts_audio_path: str,
                                       playback_position: float, interrupt_state: InterruptState,
                                       state_manager=None):
        """
        Handle detected interrupt by processing with InterruptState.

        Returns:
            StateOutput: Result of interrupt processing
        """
        try:
            self.logger.info(
                "Processing detected interrupt",
                action="handle_detected_interrupt",
                input_data={"playback_position": playback_position, "audio_path": current_tts_audio_path},
                layer="interrupt_manager"
            )

            # Prepare input data for interrupt processing
            interrupt_input = {
                "audio_data": audio_data,
                "current_tts_audio_path": current_tts_audio_path,
                "playback_position": playback_position,
                "user_input": "[Real-time interrupt detected]"
            }

            # Get current state configuration for reversibility analysis
            context = {
                "workflow_state_config": {
                    "reversible": None,
                    "has_side_effect": None,
                    "post_tts_policy": None
                },
                "memory_manager": self.memory_manager,
                "session_id": self.session_id,
                "user_id": getattr(state_manager, 'user_id', None) if state_manager else None
            }

            # Try to get state configuration if state_manager is available
            if state_manager and hasattr(state_manager, 'get_state') and hasattr(state_manager, 'current_workflow_state_id'):
                try:
                    state_config = state_manager.get_state(state_manager.current_workflow_state_id)
                    if state_config:
                        context["workflow_state_config"].update({
                            "reversible": getattr(state_config, 'reversible', None),
                            "has_side_effect": getattr(state_config, 'has_side_effect', None),
                            "post_tts_policy": getattr(state_config, 'post_tts_policy', None)
                        })
                except Exception as e:
                    self.logger.warning(f"Could not get state config: {e}")

            # Process interrupt with InterruptState
            result = await interrupt_state.process(interrupt_input, context)

            self.logger.info(
                "Interrupt processing completed",
                action="handle_detected_interrupt",
                output_data={
                    "interrupt_confirmed": result.outputs.get("interrupt_confirmed", False),
                    "action_reversible": result.outputs.get("action_reversible", "unknown")
                },
                layer="interrupt_manager"
            )

            return result

        except Exception as e:
            self.logger.error(
                "Error handling detected interrupt",
                action="handle_detected_interrupt",
                reason=str(e),
                layer="interrupt_manager"
            )
            return None

    async def process_interrupt_detection(self, audio_data: bytes, current_tts_audio_path: str = None,
                                        playback_position: float = 0.0, state_manager=None) -> StateOutput:
        """
        Process interrupt detection using the InterruptState.

        This method can be called when audio input is detected during TTS playback.
        """
        try:
            self.logger.info(
                "Processing interrupt detection",
                action="process_interrupt_detection",
                input_data={"has_audio_data": audio_data is not None, "tts_audio_path": current_tts_audio_path},
                layer="interrupt_manager"
            )

            # Get interrupt configuration
            interrupt_config = get_interrupt_config()

            # Create InterruptState instance
            interrupt_state = InterruptState(
                state_id="interrupt_detection",
                agent_registry=state_manager.agent_registry if state_manager else None,
                session_id=self.session_id,
                interrupt_config=interrupt_config
            )

            # Prepare input data for interrupt processing
            interrupt_input = {
                "audio_data": audio_data,
                "current_tts_audio_path": current_tts_audio_path,
                "playback_position": playback_position,
                "action_context": {
                    "current_state": state_manager.current_workflow_state_id if state_manager else "unknown",
                    "session_id": self.session_id
                }
            }

            # Get current state configuration for reversibility analysis
            interrupt_config = None
            reversible = None
            if state_manager and hasattr(state_manager, 'get_state') and hasattr(state_manager, 'current_workflow_state_id'):
                try:
                    state_config = state_manager.get_state(state_manager.current_workflow_state_id)
                    if state_config and hasattr(state_config, 'interrupt_config') and state_config.interrupt_config:
                        interrupt_config = state_config.interrupt_config
                        reversible = interrupt_config.reversible
                except Exception as e:
                    self.logger.warning(f"Could not get interrupt config from state: {e}")

            context = {
                "workflow_state_config": {
                    "interrupt_config": interrupt_config.model_dump() if interrupt_config else None,
                    "reversible": reversible,
                    "has_side_effect": getattr(state_config, 'has_side_effect', None) if 'state_config' in locals() else None,
                    "post_tts_policy": getattr(state_config, 'post_tts_policy', None) if 'state_config' in locals() else None
                },
                "memory_manager": self.memory_manager,
                "session_id": self.session_id,
                "user_id": getattr(state_manager, 'user_id', None) if state_manager else None
            }

            # Process interrupt detection
            result = await interrupt_state.process(interrupt_input, context)

            self.logger.info(
                "Interrupt detection processing completed",
                action="process_interrupt_detection",
                output_data={
                    "interrupt_detected": result.outputs.get("interrupt_detected", False),
                    "interrupt_confirmed": result.outputs.get("interrupt_confirmed", False)
                },
                layer="interrupt_manager"
            )

            return result

        except Exception as e:
            self.logger.error(
                "Error processing interrupt detection",
                action="process_interrupt_detection",
                reason=str(e),
                layer="interrupt_manager"
            )
            return StateOutput(
                status=StatusType.ERROR,
                message=f"Interrupt detection error: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"error": str(e)}
            )

    async def execute_queued_input_workflow(self, queued_input: str, state_manager=None):
        """
        Execute a complete workflow for queued user input with enhanced context.
        This simulates the user providing new input after interrupt completion.
        """
        try:
            self.logger.info(
                "Executing workflow for queued input",
                action="execute_queued_input_workflow",
                input_data={"queued_input": queued_input},
                layer="interrupt_manager"
            )

            if not state_manager:
                self.logger.error("StateManager reference required for workflow execution")
                return

            # Use the enhanced workflow execution method for consistency
            await self._execute_enhanced_queued_input_workflow(state_manager, queued_input)

        except Exception as e:
            self.logger.error(
                "Error executing queued input workflow",
                action="execute_queued_input_workflow",
                reason=str(e),
                layer="interrupt_manager"
            )

    def get_interrupt_status(self) -> Dict[str, Any]:
        """
        Get current interrupt system status.

        Returns:
            Dict containing interrupt system status information
        """
        return {
            "interrupt_in_progress": self.interrupt_in_progress,
            "current_tts_playback": self.current_tts_playback,
            "interrupt_config_enabled": self.interrupt_config.global_settings.enabled if self.interrupt_config else False,
            "session_id": self.session_id
        }
