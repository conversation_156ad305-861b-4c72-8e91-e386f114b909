"""
TTS Interrupt Monitoring System

This module provides real-time TTS playback monitoring with interrupt detection
and handling capabilities for the voice agent platform.
"""

import asyncio
import threading
import time
import numpy as np
from typing import Dict, Any, Optional, Callable
from datetime import datetime

try:
    import pygame
    PYGAME_AVAILABLE = True
except ImportError:
    PYGAME_AVAILABLE = False

from core.logging.logger_config import get_module_logger
from schemas.outputSchema import StateOutput, StatusType, StatusCode
from core.state_manager.state_output import InterruptState
from core.config.interrupt_config import get_interrupt_config


class TTSInterruptMonitor:
    """
    Monitors TTS playback and handles real-time interrupt detection.
    Provides pause/resume functionality and interrupt event handling.
    """
    
    def __init__(self, session_id: str, memory_manager, interrupt_config=None, agent_registry=None):
        self.session_id = session_id
        self.memory_manager = memory_manager
        self.interrupt_config = interrupt_config
        self.agent_registry = agent_registry
        self.logger = get_module_logger("TTSInterruptMonitor", session_id=session_id)
        
        # Playback state
        self.is_playing = False
        self.is_paused = False
        self.current_audio_path = None
        self.playback_start_time = None
        self.pause_time = None
        self.total_pause_duration = 0
        
        # Interrupt detection
        self.interrupt_detected = False
        self.interrupt_callback = None
        self.monitoring_active = False
        
        # Threading for background monitoring
        self.monitor_thread = None
        self.stop_monitoring_event = threading.Event()

        # Initialize pygame if available
        if PYGAME_AVAILABLE:
            try:
                pygame.mixer.init()
                self.logger.info("Pygame mixer initialized for TTS playback")
            except Exception as e:
                self.logger.warning(f"Failed to initialize pygame mixer: {e}")
    
    async def start_tts_with_interrupt_monitoring(self, audio_path: str, 
                                                interrupt_callback: Optional[Callable] = None) -> StateOutput:
        """
        Start TTS playback with real-time interrupt monitoring.
        
        Args:
            audio_path: Path to the audio file to play
            interrupt_callback: Optional callback function for interrupt events
            
        Returns:
            StateOutput with monitoring status
        """
        try:
            self.logger.info(
                "Starting TTS with interrupt monitoring",
                action="start_tts_with_interrupt_monitoring",
                input_data={"audio_path": audio_path},
                layer="tts_interrupt_monitor"
            )
            
            # Reset state
            self.current_audio_path = audio_path
            self.interrupt_callback = interrupt_callback
            self.interrupt_detected = False
            self.is_playing = True
            self.is_paused = False
            self.playback_start_time = time.time()
            self.total_pause_duration = 0
            self.stop_monitoring_event.clear()
            
            # Store TTS playback state in memory
            await self.memory_manager.set_tts_playback_state(
                audio_path=audio_path,
                status="playing",
                playback_position=0.0
            )
            
            # Start background monitoring
            self.monitoring_active = True
            self.monitor_thread = threading.Thread(
                target=self._background_monitor_worker,
                daemon=True
            )
            self.monitor_thread.start()
            
            # Start audio playback if pygame is available
            if PYGAME_AVAILABLE:
                try:
                    pygame.mixer.music.load(audio_path)
                    pygame.mixer.music.play()
                    self.logger.info("Started pygame audio playback")
                except Exception as e:
                    self.logger.warning(f"Failed to start pygame playback: {e}")
            
            return StateOutput(
                status=StatusType.SUCCESS,
                message="TTS monitoring started successfully",
                code=StatusCode.OK,
                outputs={
                    "monitoring_active": True,
                    "audio_path": audio_path,
                    "playback_started": True
                },
                meta={"tts_monitor": "active"}
            )
            
        except Exception as e:
            self.logger.error(
                "Error starting TTS with interrupt monitoring",
                action="start_tts_with_interrupt_monitoring",
                reason=str(e),
                layer="tts_interrupt_monitor"
            )
            
            return StateOutput(
                status=StatusType.ERROR,
                message=f"Failed to start TTS monitoring: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"error": str(e)}
            )
    
    async def pause_tts_playback(self) -> StateOutput:
        """
        Pause the current TTS playback.
        
        Returns:
            StateOutput with pause status
        """
        try:
            if not self.is_playing or self.is_paused:
                return StateOutput(
                    status=StatusType.ERROR,
                    message="No active playback to pause",
                    code=StatusCode.BAD_REQUEST,
                    outputs={},
                    meta={"error": "no_active_playback"}
                )
            
            self.is_paused = True
            self.pause_time = time.time()
            
            # Pause pygame playback if available
            if PYGAME_AVAILABLE:
                try:
                    pygame.mixer.music.pause()
                    self.logger.info("Paused pygame audio playback")
                except Exception as e:
                    self.logger.warning(f"Failed to pause pygame playback: {e}")
            
            # Update memory state
            current_position = self.get_current_playback_position()
            await self.memory_manager.set_tts_playback_state(
                audio_path=self.current_audio_path,
                status="paused",
                playback_position=current_position
            )
            
            self.logger.info(
                "TTS playback paused",
                action="pause_tts_playback",
                output_data={"playback_position": current_position},
                layer="tts_interrupt_monitor"
            )
            
            return StateOutput(
                status=StatusType.SUCCESS,
                message="TTS playback paused",
                code=StatusCode.OK,
                outputs={
                    "paused": True,
                    "playback_position": current_position
                },
                meta={"playback_state": "paused"}
            )
            
        except Exception as e:
            self.logger.error(
                "Error pausing TTS playback",
                action="pause_tts_playback",
                reason=str(e),
                layer="tts_interrupt_monitor"
            )
            
            return StateOutput(
                status=StatusType.ERROR,
                message=f"Failed to pause TTS playback: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"error": str(e)}
            )
    
    async def resume_tts_playback(self, resume_from_position: Optional[float] = None) -> StateOutput:
        """
        Resume TTS playback from current or specified position.
        
        Args:
            resume_from_position: Optional position to resume from (in seconds)
            
        Returns:
            StateOutput with resume status
        """
        try:
            if not self.is_paused and resume_from_position is None:
                return StateOutput(
                    status=StatusType.ERROR,
                    message="No paused playback to resume",
                    code=StatusCode.BAD_REQUEST,
                    outputs={},
                    meta={"error": "no_paused_playback"}
                )
            
            # Calculate resume position
            if resume_from_position is not None:
                # Resume from specific position (for irreversible actions)
                self.playback_start_time = time.time() - resume_from_position
                self.total_pause_duration = 0
                resume_pos = resume_from_position
            else:
                # Resume from where we paused
                if self.pause_time:
                    self.total_pause_duration += time.time() - self.pause_time
                    self.pause_time = None
                resume_pos = self.get_current_playback_position()
            
            self.is_paused = False
            
            # Resume pygame playback if available
            if PYGAME_AVAILABLE:
                try:
                    if resume_from_position is not None:
                        # For specific position, reload and play from start
                        # (pygame doesn't support seeking, so this is a limitation)
                        pygame.mixer.music.load(self.current_audio_path)
                        pygame.mixer.music.play()
                    else:
                        pygame.mixer.music.unpause()
                    self.logger.info(f"Resumed pygame audio playback from {resume_pos:.2f}s")
                except Exception as e:
                    self.logger.warning(f"Failed to resume pygame playback: {e}")
            
            # Update memory state
            await self.memory_manager.set_tts_playback_state(
                audio_path=self.current_audio_path,
                status="playing",
                playback_position=resume_pos
            )
            
            self.logger.info(
                "TTS playback resumed",
                action="resume_tts_playback",
                output_data={"resume_position": resume_pos},
                layer="tts_interrupt_monitor"
            )
            
            return StateOutput(
                status=StatusType.SUCCESS,
                message="TTS playback resumed",
                code=StatusCode.OK,
                outputs={
                    "resumed": True,
                    "playback_position": resume_pos
                },
                meta={"playback_state": "playing"}
            )
            
        except Exception as e:
            self.logger.error(
                "Error resuming TTS playback",
                action="resume_tts_playback",
                reason=str(e),
                layer="tts_interrupt_monitor"
            )
            
            return StateOutput(
                status=StatusType.ERROR,
                message=f"Failed to resume TTS playback: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"error": str(e)}
            )
    
    async def stop_monitoring(self) -> StateOutput:
        """
        Stop TTS monitoring and playback.
        
        Returns:
            StateOutput with stop status
        """
        try:
            self.monitoring_active = False
            self.stop_monitoring_event.set()
            self.is_playing = False
            self.is_paused = False
            
            # Stop pygame playback if available
            if PYGAME_AVAILABLE:
                try:
                    pygame.mixer.music.stop()
                    self.logger.info("Stopped pygame audio playback")
                except Exception as e:
                    self.logger.warning(f"Failed to stop pygame playback: {e}")
            
            # Wait for monitor thread to finish
            if self.monitor_thread and self.monitor_thread.is_alive():
                self.monitor_thread.join(timeout=2.0)
            
            # Update memory state
            await self.memory_manager.set_tts_playback_state(
                audio_path=self.current_audio_path,
                status="stopped",
                playback_position=self.get_current_playback_position()
            )
            
            self.logger.info(
                "TTS monitoring stopped",
                action="stop_monitoring",
                layer="tts_interrupt_monitor"
            )
            
            return StateOutput(
                status=StatusType.SUCCESS,
                message="TTS monitoring stopped",
                code=StatusCode.OK,
                outputs={"monitoring_stopped": True},
                meta={"tts_monitor": "stopped"}
            )
            
        except Exception as e:
            self.logger.error(
                "Error stopping TTS monitoring",
                action="stop_monitoring",
                reason=str(e),
                layer="tts_interrupt_monitor"
            )
            
            return StateOutput(
                status=StatusType.ERROR,
                message=f"Failed to stop TTS monitoring: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"error": str(e)}
            )
    
    def get_current_playback_position(self) -> float:
        """Get current playback position in seconds."""
        if not self.playback_start_time:
            return 0.0
        
        elapsed = time.time() - self.playback_start_time - self.total_pause_duration
        if self.is_paused and self.pause_time:
            elapsed -= (time.time() - self.pause_time)
        
        return max(0.0, elapsed)
    
    async def start_tts_interrupt_monitoring(self, tts_result: StateOutput, state_manager=None):
        """
        Start real-time interrupt monitoring for TTS playback.

        This method implements:
        1. Background audio playback
        2. Continuous microphone input monitoring
        3. Real-time VAD processing
        4. Automatic interrupt triggering
        """
        try:
            self.logger.info(
                "Starting real-time TTS interrupt monitoring",
                action="start_tts_interrupt_monitoring",
                input_data={"tts_audio_path": tts_result.outputs.get("audio_path")},
                layer="tts_interrupt_monitor"
            )

            # Get interrupt configuration from state_manager if available, otherwise use instance config
            if state_manager and hasattr(state_manager, 'interrupt_config') and state_manager.interrupt_config:
                interrupt_config = state_manager.interrupt_config
            elif self.interrupt_config:
                interrupt_config = self.interrupt_config
            else:
                interrupt_config = get_interrupt_config()

            audio_path = tts_result.outputs.get("audio_path")
            if not audio_path:
                self.logger.warning("No audio path provided for TTS playback")
                return StateOutput(
                    status=StatusType.ERROR,
                    message="No audio path provided",
                    code=StatusCode.BAD_REQUEST,
                    outputs={},
                    meta={}
                )

            # Check if interrupts are disabled in configuration
            if interrupt_config and not interrupt_config.global_settings.enabled:
                self.logger.info(
                    "Interrupt detection is disabled - playing TTS audio without interrupt monitoring",
                    action="start_tts_interrupt_monitoring",
                    output_data={"interrupt_enabled": False, "audio_path": audio_path},
                    layer="tts_interrupt_monitor"
                )

                # Play audio without interrupt monitoring
                await self._play_audio_without_monitoring(audio_path)

                return StateOutput(
                    status=StatusType.SUCCESS,
                    message="TTS audio played without interrupt monitoring",
                    code=StatusCode.OK,
                    outputs={"monitoring_skipped": True, "interrupt_enabled": False, "audio_played": True},
                    meta={"interrupt_detection_enabled": False}
                )

            # Create InterruptState instance
            interrupt_state = InterruptState(
                state_id="interrupt_monitor",
                agent_registry=state_manager.agent_registry if state_manager else None,
                session_id=self.session_id,
                interrupt_config=interrupt_config
            )

            # Audio path already extracted above, continue with interrupt monitoring

            # Store TTS playback state for potential interruption
            await self.memory_manager.set_tts_playback_state(
                audio_path=audio_path,
                status="playing",
                playback_position=0.0,
                message_hash=None
            )

            # Start background audio playback and interrupt monitoring
            await self.run_tts_with_interrupt_monitoring(audio_path, interrupt_state, interrupt_config, state_manager)

            self.logger.info(
                "Real-time TTS interrupt monitoring started",
                action="start_tts_interrupt_monitoring",
                output_data={"monitoring_active": True, "audio_path": audio_path},
                layer="tts_interrupt_monitor"
            )

        except Exception as e:
            self.logger.error(
                "Error starting TTS interrupt monitoring",
                action="start_tts_interrupt_monitoring",
                reason=str(e),
                layer="tts_interrupt_monitor"
            )

    async def _play_audio_without_monitoring(self, audio_path: str):
        """
        Play TTS audio without interrupt monitoring.

        This method provides simple audio playback when interrupt detection is disabled.
        """
        try:
            self.logger.info(
                "Playing TTS audio without interrupt monitoring",
                action="_play_audio_without_monitoring",
                input_data={"audio_path": audio_path},
                layer="tts_interrupt_monitor"
            )

            # Store TTS playback state in memory
            await self.memory_manager.set_tts_playback_state(
                audio_path=audio_path,
                status="playing",
                playback_position=0.0
            )

            # Use pygame for audio playback if available
            if PYGAME_AVAILABLE:
                try:
                    import pygame
                    pygame.mixer.init()
                    pygame.mixer.music.load(audio_path)
                    pygame.mixer.music.play()

                    self.logger.info("Started pygame audio playback (no monitoring)")

                    # Wait for playback to complete
                    import asyncio
                    while pygame.mixer.music.get_busy():
                        await asyncio.sleep(0.1)

                    self.logger.info("Audio playback completed")

                except Exception as e:
                    self.logger.error(f"Failed to play audio with pygame: {e}")
                    # Fallback to playsound if pygame fails
                    await self._fallback_audio_playback(audio_path)
            else:
                # Fallback to playsound if pygame not available
                await self._fallback_audio_playback(audio_path)

            # Update memory state to completed
            await self.memory_manager.set_tts_playback_state(
                audio_path=audio_path,
                status="completed",
                playback_position=0.0
            )

        except Exception as e:
            self.logger.error(
                "Error playing audio without monitoring",
                action="_play_audio_without_monitoring",
                reason=str(e),
                layer="tts_interrupt_monitor"
            )
            raise

    async def _fallback_audio_playback(self, audio_path: str):
        """Fallback audio playback using playsound."""
        try:
            import asyncio
            from playsound import playsound

            self.logger.info("Using playsound for audio playback")

            # Run playsound in a thread to avoid blocking
            await asyncio.to_thread(playsound, audio_path)

        except Exception as e:
            self.logger.error(f"Fallback audio playback failed: {e}")
            # If all else fails, just log the audio path
            self.logger.warning(f"Audio file generated but playback failed: {audio_path}")



    async def run_tts_with_interrupt_monitoring(self, audio_path: str, interrupt_state: InterruptState,
                                              interrupt_config, state_manager=None):
        """
        Run TTS playback with concurrent interrupt monitoring.
        This implements the real-time monitoring logic:
        1. Start audio playback in background thread
        2. Start microphone monitoring in parallel
        3. Process audio chunks with VAD
        4. Trigger interrupt handling when voice detected
        """
        try:
            import pygame
            import sounddevice as sd
            import numpy as np
            import threading
            import time
            import asyncio
            from utils.audio_utils import AudioProcessor

            pygame.mixer.init()
            pygame.mixer.music.load(audio_path)
            pygame.mixer.music.play()
            self.logger.info(f"Playing audio: {audio_path}")

            interrupt_event = threading.Event()
            playback_position = [0.0]

            # Add interrupt cooldown to prevent immediate re-triggering
            interrupt_cooldown_start = time.time()
            # Use configurable cooldown duration (default 0.0 for immediate interrupts like professional voice assistants)
            if interrupt_config and hasattr(interrupt_config, 'global_settings'):
                interrupt_cooldown_duration = interrupt_config.global_settings.tts_interrupt_cooldown_seconds
            elif self.interrupt_config and hasattr(self.interrupt_config, 'global_settings'):
                interrupt_cooldown_duration = self.interrupt_config.global_settings.tts_interrupt_cooldown_seconds
            else:
                # Fallback to environment variable or 0.0 for immediate interrupts
                import os
                interrupt_cooldown_duration = float(os.getenv('TTS_INTERRUPT_COOLDOWN_SECONDS', '0.0'))

            self.logger.info(f"TTS interrupt cooldown duration: {interrupt_cooldown_duration}s (0.0 = immediate interrupts like professional voice assistants)")

            def monitor_mic():
                import os  # Import os module inside the function scope
                chunk_duration = 0.6
                sample_rate = 16000
                channels = 1
                ap = AudioProcessor(interrupt_config)  # Pass interrupt config to AudioProcessor

                # Audio isolation variables to prevent TTS feedback
                tts_start_time = time.time()
                min_isolation_delay = 0.3  # Reduced to 0.3 seconds to allow faster interrupts

                self.logger.info("Starting microphone monitoring with TTS audio isolation...")
                while pygame.mixer.music.get_busy() and not interrupt_event.is_set():
                    try:
                        # Skip monitoring if interrupt monitoring is disabled
                        if state_manager and getattr(state_manager, 'skip_interrupt_monitoring', False):
                            time.sleep(0.1)
                            continue

                        # Check if interrupts are disabled in configuration
                        if interrupt_config and not interrupt_config.global_settings.enabled:
                            self.logger.debug("Interrupt detection disabled in config - skipping VAD check")
                            time.sleep(0.1)
                            continue

                        # Audio isolation: Skip monitoring immediately after TTS starts to avoid feedback
                        current_time = time.time()
                        if current_time - tts_start_time < min_isolation_delay:
                            self.logger.debug(f"Audio isolation active - skipping VAD for {min_isolation_delay - (current_time - tts_start_time):.1f}s")
                            time.sleep(0.1)
                            continue

                        # Log that we're actively monitoring
                        if int(current_time) % 2 == 0:  # Log every 2 seconds
                            self.logger.debug("Actively monitoring for voice interrupts...")

                        # Check cooldown period to prevent immediate interrupts
                        if current_time - interrupt_cooldown_start < interrupt_cooldown_duration:
                            time.sleep(0.1)
                            continue

                        # Enhanced audio capture with TTS feedback detection
                        audio = sd.rec(int(chunk_duration * sample_rate), samplerate=sample_rate, channels=channels, dtype='int16')
                        sd.wait()
                        audio_bytes = audio.tobytes()

                        # Pre-filter: Check if audio level suggests TTS feedback vs human speech
                        audio_samples = np.frombuffer(audio_bytes, dtype=np.int16)

                        # Safe RMS calculation to avoid runtime warnings
                        audio_squared = audio_samples.astype(np.float64) ** 2
                        mean_squared = np.mean(audio_squared)
                        if mean_squared > 0:
                            audio_rms = np.sqrt(mean_squared)
                        else:
                            audio_rms = 0.0

                        audio_peak = np.max(np.abs(audio_samples))

                        # TTS audio typically has consistent high amplitude, human speech is more variable
                        peak_to_rms_ratio = audio_peak / (audio_rms + 1e-6)  # Avoid division by zero

                        # Relaxed threshold - only skip extremely consistent audio (likely pure TTS feedback)
                        if peak_to_rms_ratio > 15.0 and audio_rms > 5000:  # Much higher threshold, only for very loud consistent audio
                            self.logger.debug(f"Skipping VAD - likely pure TTS feedback (peak/RMS: {peak_to_rms_ratio:.2f}, RMS: {audio_rms:.0f})")
                            time.sleep(0.1)
                            continue

                        # Use configurable threshold from interrupt config
                        vad_threshold = interrupt_config.global_settings.vad_threshold if interrupt_config else 0.1
                        self.logger.debug(f"VAD threshold: {vad_threshold}, audio RMS: {audio_rms:.2f}, peak/RMS: {peak_to_rms_ratio:.2f}")

                        vad_result = ap.detect_voice_activity(audio_bytes, threshold=vad_threshold)
                        if vad_result.status.value == 'success' and vad_result.outputs.get('has_voice'):
                            energy = vad_result.outputs.get('energy', 0)
                            self.logger.info(f"🎤 Voice activity detected! Energy: {energy:.0f}")

                            # Check if this is a very high energy signal that might be TTS feedback
                            if energy > 50000000:  # Very high energy threshold for obvious TTS feedback
                                self.logger.info("⚠️ Very high energy detected - likely TTS feedback, skipping")
                                continue

                            # For testing: Use simplified confirmation
                            # TODO: Remove this simplified version once debugging is complete
                            use_simple_confirmation = os.getenv('SIMPLE_INTERRUPT_CONFIRMATION', 'true').lower() == 'true'

                            if use_simple_confirmation:
                                # Simple confirmation: just wait a bit and check again
                                self.logger.info("Using simplified confirmation for testing...")
                                time.sleep(0.1)  # Very short delay

                                # Quick second check
                                quick_audio = sd.rec(int(0.2 * sample_rate), samplerate=sample_rate, channels=channels, dtype='int16')
                                sd.wait()
                                quick_bytes = quick_audio.tobytes()
                                quick_result = ap.detect_voice_activity(quick_bytes, threshold=vad_threshold)

                                if quick_result.status.value == 'success' and quick_result.outputs.get('has_voice'):
                                    # Voice confirmed - trigger interrupt immediately
                                    interrupt_event.set()
                                    playback_position[0] = pygame.mixer.music.get_pos() / 1000.0
                                    pygame.mixer.music.stop()
                                    self.logger.info("🎯 Voice confirmed with simple method! Triggering interrupt...")
                                    break
                                else:
                                    self.logger.info("Simple confirmation failed - no sustained voice")
                            else:
                                # Original complex confirmation process
                                self.logger.info("Using complex confirmation process...")
                                time.sleep(0.2)

                                confirm_audio = sd.rec(int(0.3 * sample_rate), samplerate=sample_rate, channels=channels, dtype='int16')
                                sd.wait()
                                confirm_bytes = confirm_audio.tobytes()

                                confirm_result = ap.detect_voice_activity(confirm_bytes, threshold=vad_threshold)
                                if confirm_result.status.value == 'success' and confirm_result.outputs.get('has_voice'):
                                    interrupt_event.set()
                                    playback_position[0] = pygame.mixer.music.get_pos() / 1000.0
                                    pygame.mixer.music.stop()
                                    self.logger.info("🎯 Voice confirmed with complex method! Triggering interrupt...")
                                    break
                                else:
                                    self.logger.info("Complex confirmation failed - no sustained voice activity")
                        time.sleep(0.1)
                    except Exception as e:
                        self.logger.error(f"Error in microphone monitoring: {e}")
                        break
                self.logger.info("Microphone monitoring stopped.")

            mic_thread = threading.Thread(target=monitor_mic, daemon=True)
            mic_thread.start()

            while pygame.mixer.music.get_busy() and not interrupt_event.is_set():
                await asyncio.sleep(0.1)

            # Ensure microphone monitoring is stopped
            if mic_thread.is_alive():
                self.logger.info("Waiting for microphone thread to stop...")
                mic_thread.join(timeout=2.0)  # Wait up to 2 seconds for thread to stop

            if interrupt_event.is_set():
                await self._handle_interrupt_during_playback(audio_path, playback_position[0], state_manager)
            else:
                self.logger.info("Playback finished without interrupt.")

            # Update TTS playback state
            final_status = "interrupted" if interrupt_event.is_set() else "completed"
            await self.memory_manager.set_tts_playback_state(
                audio_path=audio_path,
                status=final_status,
                playback_position=playback_position[0],
                message_hash=None
            )

            self.logger.info(
                f"TTS playback {final_status}",
                action="run_tts_with_interrupt_monitoring",
                output_data={"final_status": final_status, "position": playback_position[0]},
                layer="tts_interrupt_monitor"
            )

        except Exception as e:
            self.logger.error(
                "Error in TTS interrupt monitoring",
                action="run_tts_with_interrupt_monitoring",
                reason=str(e),
                layer="tts_interrupt_monitor"
            )

    async def _handle_interrupt_during_playback(self, audio_path: str, playback_position: float, state_manager=None):
        """Handle interrupt that occurred during TTS playback."""
        try:
            import pygame
            import asyncio

            self.logger.info("Handling interrupt...")
            self.logger.info("Capturing real user audio during interrupt...")

            # Capture real audio from microphone during interrupt
            captured_user_input = await self.capture_and_transcribe_interrupt_audio()
            self.logger.info(f"Transcribed user input: '{captured_user_input}'")

            # Handle interrupt through state manager if available
            if state_manager and hasattr(state_manager, 'interrupt_manager'):
                ack_result = await state_manager.interrupt_manager.handle_interrupt(
                    audio_data=None,
                    current_tts_audio_path=audio_path,
                    playback_position=playback_position,
                    user_input=captured_user_input,
                    state_manager=state_manager
                )
                ack_msg = ack_result.outputs.get("acknowledgment_message")
                self.logger.info(f"Ack message: {ack_msg}")

                # Only play acknowledgment if we have a valid message
                if ack_msg and ack_msg.strip():
                    # Set flag to skip interrupt monitoring for acknowledgment TTS
                    if hasattr(state_manager, 'skip_interrupt_monitoring'):
                        state_manager.skip_interrupt_monitoring = True
                    try:
                        tts_ack_result = await state_manager.executePipelineState({"text": ack_msg})
                        ack_audio_path = tts_ack_result.outputs.get("audio_path")
                        if ack_audio_path:
                            self.logger.info("Playing acknowledgment TTS...")
                            pygame.mixer.music.load(ack_audio_path)
                            pygame.mixer.music.play()
                            while pygame.mixer.music.get_busy():
                                await asyncio.sleep(0.1)
                            self.logger.info("Acknowledgment TTS finished.")
                    finally:
                        # Reset flag after acknowledgment TTS
                        if hasattr(state_manager, 'skip_interrupt_monitoring'):
                            state_manager.skip_interrupt_monitoring = False

                # Set queue flags for processing user input after TTS completion
                interrupt_context = await self.memory_manager.get_interrupt_context()
                user_input_queued = interrupt_context.get("user_input_queued")
                action_reversible = interrupt_context.get("action_reversible", True)

                if user_input_queued and user_input_queued not in ["[Interrupt detected]", "[User interrupted during TTS]"]:
                    if action_reversible:
                        self.logger.info(f"Queuing user input for after TTS: '{user_input_queued}'")
                        await self.memory_manager.set("contextual", "queued_user_input", user_input_queued)
                        await self.memory_manager.set("contextual", "process_queued_after_tts", True)
                    else:
                        self.logger.info(f"Irreversible action - no queuing for: '{user_input_queued}'")

                self.logger.info(f"Resuming original TTS from {playback_position:.2f} seconds...")
                pygame.mixer.music.load(audio_path)
                pygame.mixer.music.play(start=playback_position)
                while pygame.mixer.music.get_busy():
                    await asyncio.sleep(0.1)
                self.logger.info("Original TTS playback completed.")

                # Check if there's queued user input to process (for reversible actions)
                if hasattr(state_manager, '_process_queued_user_input_after_tts'):
                    await state_manager._process_queued_user_input_after_tts()

        except Exception as e:
            self.logger.error(
                "Error handling interrupt during playback",
                action="_handle_interrupt_during_playback",
                reason=str(e),
                layer="tts_interrupt_monitor"
            )

    async def process_audio_chunk_for_interrupt(self, audio_data: bytes, interrupt_state: InterruptState) -> bool:
        """
        Process audio chunk for interrupt detection using VAD.

        Returns:
            bool: True if voice activity detected, False otherwise
        """
        try:
            # Use InterruptState's VAD functionality
            voice_detected = await interrupt_state._detect_voice_activity(audio_data)
            return voice_detected

        except Exception as e:
            self.logger.error(
                "Error processing audio chunk for interrupt",
                action="process_audio_chunk_for_interrupt",
                reason=str(e),
                layer="tts_interrupt_monitor"
            )
            return False

    async def capture_and_transcribe_interrupt_audio(self, duration_sec: float = 5.0, sample_rate: int = 16000) -> str:
        """
        Capture real audio from microphone during interrupt and transcribe it to text.

        Args:
            duration_sec: Duration to record audio in seconds
            sample_rate: Audio sample rate

        Returns:
            str: Transcribed text from the captured audio
        """
        try:
            import sounddevice as sd
            import numpy as np
            import tempfile
            import wave
            import os

            self.logger.info(f"Recording {duration_sec} seconds of interrupt audio...")

            # Record audio from microphone
            audio_data = sd.rec(
                int(duration_sec * sample_rate),
                samplerate=sample_rate,
                channels=1,
                dtype='int16'
            )
            sd.wait()  # Wait for recording to complete

            # Save to temporary WAV file for STT processing
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                temp_audio_path = temp_file.name

            # Write WAV file
            with wave.open(temp_audio_path, 'wb') as wf:
                wf.setnchannels(1)
                wf.setsampwidth(2)  # 16-bit
                wf.setframerate(sample_rate)
                wf.writeframes(audio_data.tobytes())

            self.logger.info("Audio captured, transcribing...")

            # Transcribe the captured audio using STT agent
            transcribed_text = await self._transcribe_audio_file(temp_audio_path)
            self.logger.info(f"Transcription result: '{transcribed_text}'")

            # Clean up temporary file
            try:
                os.unlink(temp_audio_path)
            except:
                pass  # Ignore cleanup errors

            return transcribed_text

        except ImportError:
            self.logger.error("sounddevice not available - install with: pip install sounddevice")
            return "[Audio capture not available]"
        except Exception as e:
            self.logger.error(f"Audio capture failed: {e}")
            return f"[Audio capture error: {str(e)}]"

    async def _transcribe_audio_file(self, audio_file_path: str) -> str:
        """
        Transcribe an audio file using the STT agent.

        Args:
            audio_file_path: Path to the audio file to transcribe

        Returns:
            str: Transcribed text from the audio file
        """
        try:
            # Get agent registry from memory manager or use a direct approach
            agent_registry = getattr(self, 'agent_registry', None)

            if not agent_registry:
                # Try to get it from memory manager if available
                try:
                    # Check if we can access agent registry through memory manager
                    session_data = await self.memory_manager.get("session_metadata")
                    if session_data and "agent_registry" in session_data:
                        agent_registry = session_data["agent_registry"]
                except:
                    pass

            # --- FIX: Use getAgent method instead of key membership ---
            stt_agent = None
            if agent_registry and hasattr(agent_registry, "getAgent"):
                stt_agent = agent_registry.getAgent("stt_agent")

            if stt_agent:
                self.logger.info("Using STT agent to transcribe interrupt audio")

                # Prepare input for STT agent
                stt_input = {
                    "audio_path": audio_file_path,
                    "language": "en"  # Default to English
                }

                # Call STT agent
                stt_result = await stt_agent.process(stt_input, {})

                if stt_result.status.value == 'success':
                    transcribed_text = stt_result.outputs.get('text', '').strip()
                    if transcribed_text:
                        self.logger.info(f"STT transcription successful: '{transcribed_text}'")
                        return transcribed_text
                    else:
                        self.logger.warning("STT returned empty transcription")
                        return "[No speech detected in interrupt audio]"
                else:
                    self.logger.error(f"STT agent failed: {stt_result.message}")
                    return f"[STT error: {stt_result.message}]"
            else:
                # Fallback: Try to use a direct STT approach
                self.logger.warning("No STT agent available, attempting direct transcription")
                return await self._fallback_transcription(audio_file_path)

        except Exception as e:
            self.logger.error(f"Audio transcription failed: {e}")
            return f"[Transcription error: {str(e)}]"

    async def _fallback_transcription(self, audio_file_path: str) -> str:
        """
        Fallback transcription method when STT agent is not available.

        Args:
            audio_file_path: Path to the audio file to transcribe

        Returns:
            str: Transcribed text or error message
        """
        try:
            # Try to use speech_recognition library as fallback
            import speech_recognition as sr

            recognizer = sr.Recognizer()

            with sr.AudioFile(audio_file_path) as source:
                audio = recognizer.record(source)

            # Try Google Speech Recognition (free tier)
            try:
                text = recognizer.recognize_google(audio)
                self.logger.info(f"Fallback transcription successful: '{text}'")
                return text.strip()
            except sr.UnknownValueError:
                self.logger.warning("Fallback STT could not understand audio")
                return "[No speech detected in interrupt audio]"
            except sr.RequestError as e:
                self.logger.error(f"Fallback STT service error: {e}")
                return f"[STT service error: {str(e)}]"

        except ImportError:
            self.logger.error("speech_recognition library not available - install with: pip install SpeechRecognition")
            return "[STT not available - install SpeechRecognition library]"
        except Exception as e:
            self.logger.error(f"Fallback transcription failed: {e}")
            return f"[Fallback transcription error: {str(e)}]"

    def _background_monitor_worker(self):
        """Background worker thread for interrupt monitoring."""
        try:
            while self.monitoring_active and not self.stop_monitoring_event.is_set():
                # Check for interrupt conditions
                # This is where you would integrate real VAD or other interrupt detection

                # For now, simulate checking every 100ms
                time.sleep(0.1)

                # Check if playback is complete
                if PYGAME_AVAILABLE and not pygame.mixer.music.get_busy() and self.is_playing:
                    self.is_playing = False
                    self.monitoring_active = False
                    break

        except Exception as e:
            self.logger.error(
                "Error in background monitor worker",
                action="_background_monitor_worker",
                reason=str(e),
                layer="tts_interrupt_monitor"
            )
