from utils.mongo_client import get_mongo_client
from typing import Optional, List, Dict

async def get_user_from_mongo(user_id: str) -> dict:
    db = await get_mongo_client()
    collection = db["users"]
    doc = await collection.find_one({"user_id": user_id})
    if doc:
        doc["_id"] = str(doc["_id"])
    return doc

async def get_call_sessions_from_mongo(session_id: Optional[str] = None, user_id: Optional[str] = None) -> List[dict]:
    db = await get_mongo_client()
    collection = db["call_sessions"]
    query = {}
    if session_id:
        query["session_id"] = session_id
    if user_id:
        query["user_id"] = user_id
    cursor = collection.find(query)
    results = []
    async for doc in cursor:
        doc["_id"] = str(doc["_id"])
        results.append(doc)
    return results

async def get_pipeline_from_mongo(pipeline_id: str) -> dict:
    db = await get_mongo_client()
    collection = db["pipelines"]
    doc = await collection.find_one({"pipeline_id": pipeline_id})
    if doc:
        doc["_id"] = str(doc["_id"])
    return doc

async def get_intent_history_from_mongo(session_id: Optional[str] = None, user_id: Optional[str] = None) -> List[dict]:
    db = await get_mongo_client()
    collection = db["intent_history"]
    query = {}
    if session_id:
        query["session_id"] = session_id
    if user_id:
        query["user_id"] = user_id
    cursor = collection.find(query)
    results = []
    async for doc in cursor:
        doc["_id"] = str(doc["_id"])
        results.append(doc)
    return results

async def get_dialog_logs_from_mongo(session_id: Optional[str] = None, user_id: Optional[str] = None) -> List[dict]:
    db = await get_mongo_client()
    collection = db["dialog"]
    query = {}
    if session_id:
        query["session_id"] = session_id
    if user_id:
        query["user_id"] = user_id
    cursor = collection.find(query)
    results = []
    async for doc in cursor:
        doc["_id"] = str(doc["_id"])
        results.append(doc)
    return results 