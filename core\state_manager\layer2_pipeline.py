from typing import Dict, Any, List, Optional
import time
import asyncio
import sys
from pathlib import Path
from pydantic import ValidationError

from core.state_mgmt.memory_manager import MemoryManager

# Add project root to path for imports
project_root = str(Path(__file__).parent.parent.parent)
sys.path.append(project_root)

from agents.base_agent import BaseAgent
from core.exceptions import PipelineError, Layer2Error
from core.logger_config import get_module_logger
from schemas.outputSchema import StateOutput, StatusType, StatusCode
from core.state_mgmt.states.Layer2 import Layer2, PipelineStep
from .mcp_mock import MockMCPAgent
from core.agent_registry import AgentRegistry, REGISTRY_REDIS_KEY
from schemas.agent_metadata import AgentMetadata
# from .Layer2SchemaLoader import Layer2SchemaLoader

class Layer2Pipeline:
    """
    Executes a sequence of procedural steps (Layer 2) for a given state.
    """
    def __init__(self, layer2_config: Layer2, tools_registry: AgentRegistry, memory_interface: MemoryManager, state_id: str):
        self.state_id = state_id
        self.memory: MemoryManager = memory_interface
        self.tools_registry = tools_registry
        self.logger = get_module_logger(f"layer2_pipeline_{state_id}", state_id=state_id)
        self.layer2_config = layer2_config
        self.steps = self.layer2_config.pipeline
        if not self.steps:
            raise Layer2Error(f"Layer2 configuration for state '{state_id}' has no steps defined")
    
    async def run(self, input_data: dict, session_context: dict) -> StateOutput:
        """
        Executes the pipeline steps in order, calling MCP-compliant agents.
        
        Args:
            input_data (dict): Input data for the pipeline.
            session_context (dict): Session context (e.g., session_id, user_id).
        
        Returns:
            StateOutput: Standardized output with status, outputs, metrics, and logs.
        """
        start_time = time.time()
        logs = []
        metrics = {"duration_ms": 0}
        outputs = {}
        status = StatusType.SUCCESS
        current_input = input_data.copy()
        
        try:
            for step_idx, step in enumerate(self.steps):
                step_start_time = time.time()
                step_name = step.step
                agent_name = step.agent
                
                self.logger.debug("Executing pipeline step", step=step_name, agent_name=agent_name, input_data=current_input)
                
                # Get agent from tools_registry
                agent:BaseAgent = self.tools_registry.getAgent(agent_name)
            
                if not agent:
                    raise PipelineError(f"Agent '{agent_name}' not found in tools registry")
                
                # Prepare step context
                step_context = {
                    **session_context,
                    "step_input": current_input,
                    "memory": self.memory
                }
                
                # Execute agent
                try:
                    step_input = {}
                    for input_key, input_ref in step.input.items():
                        if memory_value := await self.memory.get(input_ref):
                            step_input[input_key] = memory_value
                    for input_key, input_value in current_input.items():
                        step_input[input_key] = input_value
                    
                    agent_output = await agent.process(step_input, step_context)
            
                    for output_key, output_value in agent_output.outputs.items():
                        outputs[output_key] = output_value
                        
                    # Update current_input for next step
                    for output_key, output_value in agent_output.outputs.items():
                        await self.memory.set("contextual", output_key, output_value)
                    
                    # Log step success
                    step_duration = (time.time() - step_start_time) * 1000
                    logs.append({
                        "step": step_name,
                        "latency": step_duration,
                        "status": "success",
                        "message": f"Completed step {step_name}"
                    })
                    metrics[f"step_{step_name}_ms"] = int(step_duration)
    
                except Exception as e:
                    # Handle errors based on onError configuration
                    if self.layer2_config.onError and self.layer2_config.onError.retry > 0:
                        self.logger.warning("Retrying step due to error", step=step_name, reason=str(e))
                        # Retry logic (simplified)
                        for attempt in range(self.layer2_config.onError.retry):
                            try:
                                agent_output = await agent.process(current_input, step_context)
                                outputs.update(agent_output)
                                step_duration = (time.time() - step_start_time) * 1000
                                logs.append({
                                    "step": step_name,
                                    "latency": step_duration,
                                    "status": "success",
                                    "message": f"Completed step {step_name} after retry"
                                })
                                metrics[f"step_{step_name}_ms"] = int(step_duration)
                                break
                            except Exception as retry_e:
                                self.logger.warning("Retry failed", step=step_name, attempt=attempt + 1, reason=str(retry_e))
                        else:
                            status = StatusType.ERROR
                            error_msg = f"Step '{step_name}' failed after retries: {str(e)}"
                            logs.append({"step": step_name, "status": "failure", "message": error_msg})
                            raise PipelineError(error_msg)
                    else:
                        status = StatusType.ERROR
                        error_msg = f"Step '{step_name}' failed: {str(e)}"
                        logs.append({"step": step_name, "status": "failure", "message": error_msg})
                        raise PipelineError(error_msg)
                
                # Check for interrupts
                if self.layer2_config.onInterrupt and await self._check_interrupt(session_context):
                    status = StatusType.ERROR
                    error_msg = f"Pipeline interrupted at step '{step_name}'"
                    logs.append({"step": step_name, "status": "interrupt", "message": error_msg})
                    outputs["handler"] = self.layer2_config.onInterrupt.handler
                    outputs["resume_from"] = self.layer2_config.onInterrupt.resume_from
                    break
            
        except PipelineError as e:
            status = StatusType.ERROR
            self.logger.error("Pipeline execution failed", state_id=self.state_id, reason=str(e))
            outputs["error"] = str(e)
        except Exception as e:
            status = StatusType.ERROR
            self.logger.error("Unexpected error in pipeline execution", state_id=self.state_id, reason=str(e))
            outputs["error"] = f"Unexpected error: {str(e)}"
        
        # Calculate total duration
        metrics["duration_ms"] = int((time.time() - start_time) * 1000)
        
        return StateOutput(
            status=status,
            message="Pipeline execution completed" if status == StatusType.SUCCESS else "Pipeline execution failed",
            code=StatusCode.OK if status == StatusType.SUCCESS else StatusCode.INTERNAL_ERROR,
            outputs=outputs,
            meta={"logs": logs, "metrics": metrics}
        )
    
    async def _check_interrupt(self, session_context: dict) -> bool:
        """
        Checks for interrupt conditions (placeholder for actual interrupt logic).
        """
        # TODO: Implement actual interrupt detection (e.g., check session_context for interrupt flag)
        return False