#!/usr/bin/env python3
"""
Debug script to identify why interrupts aren't triggering when speaking during TTS.

This script will help identify the exact point where the interrupt process fails.
"""

import asyncio
import os
import sys
import time
import numpy as np
from pathlib import Path

# Add project root to path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '.')))

async def debug_vad_detection():
    """Test VAD detection with current settings."""
    print("🎤 Debugging VAD Detection")
    print("=" * 30)
    
    try:
        from utils.audio_utils import AudioProcessor
        
        # Enable debug mode for detailed VAD logging
        os.environ['VAD_DEBUG_MODE'] = 'true'
        
        processor = AudioProcessor()
        
        print("Testing VAD with current energy-based method...")
        print("Please speak now for 2 seconds...")
        
        # Record audio for testing
        try:
            import sounddevice as sd
            
            sample_rate = 16000
            duration = 2.0  # 2 seconds
            
            print("Recording...")
            audio = sd.rec(int(duration * sample_rate), samplerate=sample_rate, channels=1, dtype='int16')
            sd.wait()
            audio_bytes = audio.tobytes()
            
            print("Analyzing recorded audio...")
            
            # Test with different thresholds
            thresholds = [0.01, 0.05, 0.1, 0.5]
            
            for threshold in thresholds:
                result = processor.detect_voice_activity(audio_bytes, threshold=threshold)
                if result.status.value == 'success':
                    has_voice = result.outputs.get('has_voice', False)
                    energy = result.outputs.get('energy', 0)
                    print(f"Threshold {threshold}: has_voice={has_voice}, energy={energy:.0f}")
                else:
                    print(f"Threshold {threshold}: VAD failed - {result.message}")
            
            return True
            
        except Exception as e:
            print(f"❌ Audio recording failed: {e}")
            return False
            
    except Exception as e:
        print(f"❌ VAD debug failed: {e}")
        return False

async def debug_interrupt_configuration():
    """Debug the interrupt configuration in the banking workflow."""
    print("\n⚙️ Debugging Interrupt Configuration")
    print("=" * 40)
    
    try:
        from core.state_manager.state_manager import StateManager
        import json
        
        # Check workflow configuration
        workflow_path = Path("workflows/banking_workflow.json")
        if workflow_path.exists():
            with open(workflow_path, 'r') as f:
                workflow_data = json.load(f)
            
            interrupt_config = workflow_data.get('workflow', {}).get('interrupt_config', {})
            global_settings = interrupt_config.get('global_settings', {})
            
            print("Workflow interrupt configuration:")
            print(f"  enabled: {global_settings.get('enabled', 'NOT SET')}")
            print(f"  vad_threshold: {global_settings.get('vad_threshold', 'NOT SET')}")
            print(f"  confirmation_window_seconds: {global_settings.get('confirmation_window_seconds', 'NOT SET')}")
            print(f"  min_interrupt_duration_seconds: {global_settings.get('min_interrupt_duration_seconds', 'NOT SET')}")
            print(f"  tts_interrupt_cooldown_seconds: {global_settings.get('tts_interrupt_cooldown_seconds', 'NOT SET')}")
            
            if not global_settings.get('enabled', False):
                print("❌ INTERRUPTS ARE DISABLED IN WORKFLOW!")
                print("   This is why interrupts aren't working.")
                return False
            else:
                print("✅ Interrupts are enabled in workflow")
                
        # Test StateManager configuration
        print("\nTesting StateManager configuration...")
        state_manager = await StateManager.create("banking_workflow.json", "debug_test", "debug_user")
        
        if state_manager.interrupt_config:
            enabled = state_manager.interrupt_config.global_settings.enabled
            threshold = state_manager.interrupt_config.global_settings.vad_threshold
            
            print(f"StateManager interrupt enabled: {enabled}")
            print(f"StateManager VAD threshold: {threshold}")
            
            if enabled:
                print("✅ StateManager has interrupts enabled")
                return True
            else:
                print("❌ StateManager has interrupts disabled")
                return False
        else:
            print("❌ StateManager has no interrupt configuration")
            return False
            
    except Exception as e:
        print(f"❌ Configuration debug failed: {e}")
        return False

async def debug_audio_isolation():
    """Debug the audio isolation timing."""
    print("\n⏱️ Debugging Audio Isolation Timing")
    print("=" * 35)
    
    try:
        # Simulate the isolation delay
        isolation_delay = 0.3  # Current setting
        
        print(f"Current audio isolation delay: {isolation_delay} seconds")
        print("This means interrupts are blocked for the first 0.3s of TTS playback")
        
        if isolation_delay > 0.5:
            print("⚠️ Isolation delay may be too long for responsive interrupts")
        else:
            print("✅ Isolation delay should allow interrupts after initial TTS")
            
        return True
        
    except Exception as e:
        print(f"❌ Audio isolation debug failed: {e}")
        return False

async def debug_confirmation_process():
    """Debug the confirmation process parameters."""
    print("\n✅ Debugging Confirmation Process")
    print("=" * 35)
    
    try:
        print("Current confirmation settings:")
        print("  - Confirmation delay: 0.2 seconds")
        print("  - Confirmation duration: 0.3 seconds")
        print("  - Peak-to-RMS rejection threshold: 20.0")
        print("  - RMS rejection threshold: 8000")
        
        print("\nConfirmation process:")
        print("1. Initial VAD detects voice")
        print("2. Wait 0.2 seconds")
        print("3. Record 0.3 seconds of confirmation audio")
        print("4. Check if confirmation audio looks like TTS feedback")
        print("5. Run VAD on confirmation audio")
        print("6. If confirmed, trigger interrupt")
        
        print("\n💡 If interrupts still don't work, the issue is likely:")
        print("   - VAD threshold too high for your voice")
        print("   - Confirmation process rejecting your voice as TTS feedback")
        print("   - Audio input device issues")
        
        return True
        
    except Exception as e:
        print(f"❌ Confirmation debug failed: {e}")
        return False

async def main():
    """Run all debug tests."""
    print("🐛 Interrupt System Debug Analysis")
    print("=" * 40)
    
    tests = [
        ("Interrupt Configuration", debug_interrupt_configuration),
        ("VAD Detection", debug_vad_detection),
        ("Audio Isolation Timing", debug_audio_isolation),
        ("Confirmation Process", debug_confirmation_process)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*50}")
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n{'='*50}")
    print("📊 Debug Results Summary:")
    print("=" * 30)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\n🔧 Recommendations:")
    print("1. Ensure interrupts are enabled in banking_workflow.json")
    print("2. Try speaking louder or closer to the microphone")
    print("3. Test with VAD_DEBUG_MODE=true for detailed logs")
    print("4. Consider lowering the VAD threshold if your voice isn't detected")

if __name__ == "__main__":
    asyncio.run(main())
