#!/usr/bin/env python3
"""
Test script to validate that the integration test interrupt issue is fixed.

This script specifically tests the scenario from test_audio_interrupt_pipeline.py
to ensure that workflow-level interrupt configuration is properly respected.
"""

import asyncio
import os
import sys
import json
from pathlib import Path

# Add project root to path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '.')))

async def test_integration_scenario():
    """Test the exact scenario from test_audio_interrupt_pipeline.py"""
    print("🧪 Testing Integration Scenario (Banking Workflow)")
    print("=" * 50)
    
    try:
        from core.state_manager.state_manager import StateManager
        
        # Create StateManager exactly like the integration test
        workflow_name = "banking_workflow.json"
        session_id = "integration_test_fix"
        user_id = "test_user"
        
        print(f"Creating StateManager with workflow: {workflow_name}")
        state_manager = await StateManager.create(workflow_name, session_id, user_id)
        
        # Check the interrupt configuration
        print("\n📋 Interrupt Configuration Analysis:")
        if state_manager.interrupt_config:
            enabled = state_manager.interrupt_config.global_settings.enabled
            vad_threshold = state_manager.interrupt_config.global_settings.vad_threshold
            cooldown = state_manager.interrupt_config.global_settings.tts_interrupt_cooldown_seconds
            
            print(f"   Interrupt Enabled: {enabled}")
            print(f"   VAD Threshold: {vad_threshold}")
            print(f"   Cooldown Duration: {cooldown}s")
            
            # Verify workflow configuration is applied
            workflow_path = Path("workflows/banking_workflow.json")
            with open(workflow_path, 'r') as f:
                workflow_data = json.load(f)
            
            expected_enabled = workflow_data['workflow']['interrupt_config']['global_settings']['enabled']
            expected_threshold = workflow_data['workflow']['interrupt_config']['global_settings']['vad_threshold']
            
            print(f"\n   Expected from workflow: enabled={expected_enabled}, threshold={expected_threshold}")
            print(f"   Actual from StateManager: enabled={enabled}, threshold={vad_threshold}")
            
            if enabled == expected_enabled and vad_threshold == expected_threshold:
                print("   ✅ Workflow configuration correctly applied")
                config_correct = True
            else:
                print("   ❌ Workflow configuration not applied correctly")
                config_correct = False
        else:
            print("   ❌ No interrupt configuration found")
            config_correct = False
        
        # Test interrupt manager configuration
        print("\n🔧 Interrupt Manager Configuration:")
        if state_manager.interrupt_manager and state_manager.interrupt_manager.interrupt_config:
            manager_enabled = state_manager.interrupt_manager.interrupt_config.global_settings.enabled
            print(f"   InterruptManager enabled: {manager_enabled}")
            
            if manager_enabled == expected_enabled:
                print("   ✅ InterruptManager has correct configuration")
                manager_correct = True
            else:
                print("   ❌ InterruptManager configuration mismatch")
                manager_correct = False
        else:
            print("   ❌ InterruptManager not properly configured")
            manager_correct = False
        
        # Test TTS monitor configuration
        print("\n🎵 TTS Monitor Configuration:")
        if state_manager.tts_monitor and state_manager.tts_monitor.interrupt_config:
            monitor_enabled = state_manager.tts_monitor.interrupt_config.global_settings.enabled
            print(f"   TTSMonitor enabled: {monitor_enabled}")
            
            if monitor_enabled == expected_enabled:
                print("   ✅ TTSMonitor has correct configuration")
                monitor_correct = True
            else:
                print("   ❌ TTSMonitor configuration mismatch")
                monitor_correct = False
        else:
            print("   ❌ TTSMonitor not properly configured")
            monitor_correct = False
        
        # Test TTS interrupt monitoring behavior
        print("\n🚫 Testing TTS Interrupt Monitoring Behavior:")
        
        # Create a mock TTS result
        from schemas.outputSchema import StateOutput, StatusType, StatusCode
        mock_tts_result = StateOutput(
            status=StatusType.SUCCESS,
            message="Mock TTS result",
            code=StatusCode.OK,
            outputs={"audio_path": "mock_audio.wav"},
            meta={}
        )
        
        # Test if interrupt monitoring is properly skipped when disabled
        try:
            result = await state_manager.tts_monitor.start_tts_interrupt_monitoring(
                mock_tts_result, 
                state_manager=state_manager
            )
            
            if not expected_enabled:  # If interrupts should be disabled
                if result.outputs.get('monitoring_skipped', False):
                    print("   ✅ TTS interrupt monitoring correctly skipped (disabled)")
                    monitoring_correct = True
                else:
                    print("   ❌ TTS interrupt monitoring not skipped when disabled")
                    monitoring_correct = False
            else:  # If interrupts should be enabled
                if not result.outputs.get('monitoring_skipped', False):
                    print("   ✅ TTS interrupt monitoring correctly started (enabled)")
                    monitoring_correct = True
                else:
                    print("   ❌ TTS interrupt monitoring incorrectly skipped when enabled")
                    monitoring_correct = False
                    
        except Exception as e:
            print(f"   ❌ Error testing TTS interrupt monitoring: {e}")
            monitoring_correct = False
        
        # Overall result
        all_correct = config_correct and manager_correct and monitor_correct and monitoring_correct
        
        print(f"\n📊 Test Results:")
        print(f"   Configuration Loading: {'✅' if config_correct else '❌'}")
        print(f"   InterruptManager Setup: {'✅' if manager_correct else '❌'}")
        print(f"   TTSMonitor Setup: {'✅' if monitor_correct else '❌'}")
        print(f"   Monitoring Behavior: {'✅' if monitoring_correct else '❌'}")
        
        if all_correct:
            print("\n🎉 Integration test fix successful!")
            print("   The banking workflow interrupt configuration is now properly respected.")
            return True
        else:
            print("\n⚠️ Integration test fix incomplete.")
            print("   Some components are not respecting the workflow configuration.")
            return False
            
    except Exception as e:
        print(f"❌ Integration test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_vad_configuration_propagation():
    """Test that VAD configuration is properly propagated through the system."""
    print("\n🎤 Testing VAD Configuration Propagation")
    print("=" * 40)
    
    try:
        from utils.audio_utils import AudioProcessor
        from core.config.interrupt_config import InterruptConfig, GlobalInterruptSettings
        import numpy as np
        
        # Create disabled interrupt config
        disabled_config = InterruptConfig(
            global_settings=GlobalInterruptSettings(
                enabled=False,
                vad_threshold=0.05,
                confirmation_window_seconds=0.5,
                min_interrupt_duration_seconds=0.3,
                tts_interrupt_cooldown_seconds=0.0
            )
        )
        
        # Test AudioProcessor with disabled config
        processor = AudioProcessor(interrupt_config=disabled_config)
        
        # Create test audio that would normally trigger
        test_audio = (np.sin(2 * np.pi * 440 * np.linspace(0, 1, 16000)) * 0.8 * 32767).astype(np.int16).tobytes()
        
        result = processor.detect_voice_activity(test_audio)
        
        if result.outputs.get('detection_disabled', False):
            print("   ✅ AudioProcessor correctly respects disabled configuration")
            return True
        else:
            print("   ❌ AudioProcessor does not respect disabled configuration")
            return False
            
    except Exception as e:
        print(f"   ❌ VAD configuration test failed: {e}")
        return False

async def main():
    """Run integration test fix validation."""
    print("🔧 Integration Test Interrupt Fix Validation")
    print("=" * 60)
    
    tests = [
        ("Integration Scenario", test_integration_scenario),
        ("VAD Configuration Propagation", test_vad_configuration_propagation)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n📊 Final Results:")
    print("=" * 30)
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("\n🎉 All integration tests passed!")
        print("The test_audio_interrupt_pipeline.py should now respect workflow configuration.")
    else:
        print("\n⚠️ Some integration tests failed.")
        print("The interrupt system may still have configuration issues.")

if __name__ == "__main__":
    asyncio.run(main())
