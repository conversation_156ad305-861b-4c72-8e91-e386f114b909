#!/usr/bin/env python3
"""
Test script to validate interrupt configuration hierarchy and VAD improvements.

This script tests:
1. Workflow-level interrupt configuration override
2. VAD false positive reduction
3. Configuration precedence validation
4. End-to-end interrupt system behavior
"""

import asyncio
import os
import sys
import json
from pathlib import Path

# Add project root to path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '.')))

async def test_configuration_hierarchy():
    """Test that workflow-level interrupt configuration properly overrides defaults."""
    print("🔧 Testing Configuration Hierarchy...")
    
    try:
        from core.config.interrupt_config import get_interrupt_config
        
        # Test 1: Default configuration (no workflow override)
        print("\n1. Testing default configuration:")
        default_config = get_interrupt_config()
        print(f"   Default enabled: {default_config.global_settings.enabled}")
        print(f"   Default VAD threshold: {default_config.global_settings.vad_threshold}")
        
        # Test 2: Workflow configuration override (banking_workflow.json has enabled=false)
        print("\n2. Testing workflow configuration override:")
        
        # Load banking workflow
        workflow_path = Path("workflows/banking_workflow.json")
        if workflow_path.exists():
            with open(workflow_path, 'r') as f:
                workflow_data = json.load(f)
            
            # Get configuration with workflow override
            workflow_config = get_interrupt_config(workflow_data)
            print(f"   Workflow enabled: {workflow_config.global_settings.enabled}")
            print(f"   Workflow VAD threshold: {workflow_config.global_settings.vad_threshold}")
            
            # Validate that workflow override works
            expected_enabled = workflow_data['workflow']['interrupt_config']['global_settings']['enabled']
            actual_enabled = workflow_config.global_settings.enabled
            
            if actual_enabled == expected_enabled:
                print(f"   ✅ Configuration override working: {actual_enabled}")
                return True
            else:
                print(f"   ❌ Configuration override failed: expected {expected_enabled}, got {actual_enabled}")
                return False
        else:
            print("   ❌ Banking workflow file not found")
            return False
            
    except Exception as e:
        print(f"   ❌ Configuration test failed: {e}")
        return False

async def test_state_manager_integration():
    """Test that StateManager properly loads workflow-specific interrupt configuration."""
    print("\n🏗️ Testing StateManager Integration...")
    
    try:
        from core.state_manager.state_manager import StateManager
        
        # Create StateManager with banking workflow (has enabled=false)
        print("   Creating StateManager with banking workflow...")
        state_manager = await StateManager.create(
            workflow_name="banking_workflow.json",
            session_id="config_test",
            user_id="test_user"
        )
        
        # Check interrupt configuration
        if state_manager.interrupt_config:
            enabled = state_manager.interrupt_config.global_settings.enabled
            vad_threshold = state_manager.interrupt_config.global_settings.vad_threshold
            
            print(f"   StateManager interrupt enabled: {enabled}")
            print(f"   StateManager VAD threshold: {vad_threshold}")
            
            # Should be disabled based on banking_workflow.json
            if not enabled:
                print("   ✅ StateManager correctly loaded workflow interrupt config")
                return True
            else:
                print("   ❌ StateManager failed to apply workflow interrupt config")
                return False
        else:
            print("   ❌ StateManager has no interrupt configuration")
            return False
            
    except Exception as e:
        print(f"   ❌ StateManager integration test failed: {e}")
        return False

async def test_vad_improvements():
    """Test VAD false positive reduction improvements."""
    print("\n🎤 Testing VAD Improvements...")
    
    try:
        from utils.audio_utils import AudioProcessor
        import numpy as np
        
        # Create test audio processor
        processor = AudioProcessor()
        
        # Test 1: Silent audio (should not trigger)
        print("   Testing silent audio...")
        silent_audio = np.zeros(16000, dtype=np.int16).tobytes()  # 1 second of silence
        result = processor.detect_voice_activity(silent_audio, threshold=0.05)
        
        if result.outputs.get('has_voice', True) == False:
            print("   ✅ Silent audio correctly detected as no voice")
        else:
            print("   ❌ Silent audio incorrectly detected as voice")
            
        # Test 2: Low-level noise (should not trigger with improved threshold)
        print("   Testing low-level noise...")
        noise_audio = (np.random.normal(0, 0.01, 16000) * 32767).astype(np.int16).tobytes()
        result = processor.detect_voice_activity(noise_audio, threshold=0.05)
        
        if result.outputs.get('has_voice', True) == False:
            print("   ✅ Low-level noise correctly detected as no voice")
        else:
            print("   ❌ Low-level noise incorrectly detected as voice")
            
        # Test 3: Higher energy signal (should trigger)
        print("   Testing higher energy signal...")
        signal_audio = (np.sin(2 * np.pi * 440 * np.linspace(0, 1, 16000)) * 0.5 * 32767).astype(np.int16).tobytes()
        result = processor.detect_voice_activity(signal_audio, threshold=0.05)
        
        if result.outputs.get('has_voice', False) == True:
            print("   ✅ Higher energy signal correctly detected as voice")
        else:
            print("   ❌ Higher energy signal incorrectly detected as no voice")
            
        return True
        
    except Exception as e:
        print(f"   ❌ VAD improvement test failed: {e}")
        return False

async def test_interrupt_system_disabled():
    """Test that interrupt system respects disabled configuration."""
    print("\n🚫 Testing Interrupt System Disabled State...")
    
    try:
        from utils.audio_utils import AudioProcessor
        from core.config.interrupt_config import InterruptConfig, GlobalInterruptSettings
        import numpy as np
        
        # Create disabled interrupt config
        disabled_config = InterruptConfig(
            global_settings=GlobalInterruptSettings(
                enabled=False,
                vad_threshold=0.05,
                confirmation_window_seconds=0.5,
                min_interrupt_duration_seconds=0.3,
                tts_interrupt_cooldown_seconds=0.0
            )
        )
        
        # Create audio processor with disabled config
        processor = AudioProcessor(interrupt_config=disabled_config)
        
        # Test with loud audio that would normally trigger
        loud_audio = (np.sin(2 * np.pi * 440 * np.linspace(0, 1, 16000)) * 0.8 * 32767).astype(np.int16).tobytes()
        result = processor.detect_voice_activity(loud_audio)
        
        # Should return detection_disabled=True and has_voice=False
        if result.outputs.get('detection_disabled', False) and not result.outputs.get('has_voice', True):
            print("   ✅ Interrupt system correctly disabled")
            return True
        else:
            print("   ❌ Interrupt system not properly disabled")
            return False
            
    except Exception as e:
        print(f"   ❌ Disabled interrupt test failed: {e}")
        return False

async def main():
    """Run all configuration and VAD tests."""
    print("🧪 Interrupt Configuration & VAD Fix Validation")
    print("=" * 50)
    
    tests = [
        ("Configuration Hierarchy", test_configuration_hierarchy),
        ("StateManager Integration", test_state_manager_integration),
        ("VAD Improvements", test_vad_improvements),
        ("Interrupt System Disabled", test_interrupt_system_disabled)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n📊 Test Results Summary:")
    print("=" * 30)
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! Configuration fixes are working correctly.")
    else:
        print("⚠️ Some tests failed. Please review the issues above.")

if __name__ == "__main__":
    asyncio.run(main())
