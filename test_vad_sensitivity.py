#!/usr/bin/env python3
"""
VAD Sensitivity Testing Tool

This tool helps you test and adjust the VAD (Voice Activity Detection) threshold
to prevent false positives while maintaining good sensitivity to actual speech.
"""

import asyncio
import os
import sys
import time
import numpy as np
from dotenv import load_dotenv

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

load_dotenv()

from utils.audio_utils import AudioProcessor
from core.config.interrupt_config import get_interrupt_config


async def test_background_noise():
    """Test VAD sensitivity with background noise (no speech)."""
    print("🔇 Testing background noise sensitivity...")
    print("Please remain SILENT for the next 5 seconds.")
    print("This will help us detect false positives.")
    
    input("Press Enter when ready to start background noise test...")
    
    try:
        import sounddevice as sd
        
        # Record 5 seconds of background noise
        duration = 5.0
        sample_rate = 16000
        print(f"Recording {duration} seconds of background noise...")
        
        audio_data = sd.rec(int(duration * sample_rate), samplerate=sample_rate, channels=1, dtype='int16')
        sd.wait()
        audio_bytes = audio_data.tobytes()
        
        # Test with different thresholds
        thresholds = [0.01, 0.05, 0.1, 0.2, 0.3, 0.5]
        processor = AudioProcessor()
        
        print("\nBackground Noise Test Results:")
        print("Threshold | Voice Detected | Energy Level")
        print("-" * 40)
        
        for threshold in thresholds:
            result = processor.detect_voice_activity(audio_bytes, threshold=threshold)
            has_voice = result.outputs.get('has_voice', False)
            energy = result.outputs.get('energy', 0)
            
            status = "❌ FALSE POSITIVE" if has_voice else "✅ Correct (No Voice)"
            print(f"{threshold:8.2f} | {status:15} | {energy:.6f}")
        
        return audio_bytes
        
    except ImportError:
        print("❌ sounddevice not available. Install with: pip install sounddevice")
        return None
    except Exception as e:
        print(f"❌ Error during background noise test: {e}")
        return None


async def test_speech_detection():
    """Test VAD sensitivity with actual speech."""
    print("\n🎤 Testing speech detection sensitivity...")
    print("Please speak normally for the next 3 seconds.")
    print("Say something like: 'Hello, this is a test of voice detection.'")
    
    input("Press Enter when ready to start speech test...")
    
    try:
        import sounddevice as sd
        
        # Record 3 seconds of speech
        duration = 3.0
        sample_rate = 16000
        print(f"Recording {duration} seconds of speech... SPEAK NOW!")
        
        audio_data = sd.rec(int(duration * sample_rate), samplerate=sample_rate, channels=1, dtype='int16')
        sd.wait()
        audio_bytes = audio_data.tobytes()
        
        # Test with different thresholds
        thresholds = [0.01, 0.05, 0.1, 0.2, 0.3, 0.5]
        processor = AudioProcessor()
        
        print("\nSpeech Detection Test Results:")
        print("Threshold | Voice Detected | Energy Level")
        print("-" * 40)
        
        for threshold in thresholds:
            result = processor.detect_voice_activity(audio_bytes, threshold=threshold)
            has_voice = result.outputs.get('has_voice', False)
            energy = result.outputs.get('energy', 0)
            
            status = "✅ Correct (Voice)" if has_voice else "❌ MISSED SPEECH"
            print(f"{threshold:8.2f} | {status:15} | {energy:.6f}")
        
        return audio_bytes
        
    except ImportError:
        print("❌ sounddevice not available. Install with: pip install sounddevice")
        return None
    except Exception as e:
        print(f"❌ Error during speech test: {e}")
        return None


async def test_current_config():
    """Test the current VAD configuration."""
    print("\n⚙️ Testing current VAD configuration...")
    
    config = get_interrupt_config()
    current_threshold = config.global_settings.vad_threshold
    
    print(f"Current VAD threshold: {current_threshold}")
    print(f"Current cooldown period: {config.global_settings.tts_interrupt_cooldown_seconds}s")
    print(f"Current confirmation window: {config.global_settings.confirmation_window_seconds}s")
    
    return current_threshold


def recommend_threshold(background_energy, speech_energy):
    """Recommend an optimal threshold based on test results."""
    if background_energy is None or speech_energy is None:
        return None
    
    # Calculate a threshold that's above background noise but below speech
    margin_factor = 2.0  # Safety margin
    recommended = background_energy * margin_factor
    
    # Ensure it's below speech energy
    if recommended >= speech_energy:
        recommended = speech_energy * 0.5
    
    # Clamp to reasonable range
    recommended = max(0.01, min(0.5, recommended))
    
    return recommended


async def main():
    """Run VAD sensitivity tests and provide recommendations."""
    print("🎯 VAD Sensitivity Testing Tool")
    print("=" * 50)
    print("This tool helps you find the optimal VAD threshold to prevent false interrupts.")
    print()
    
    # Test current configuration
    current_threshold = await test_current_config()
    
    # Test background noise
    background_audio = await test_background_noise()
    
    # Test speech detection
    speech_audio = await test_speech_detection()
    
    # Analyze results and provide recommendations
    if background_audio and speech_audio:
        print("\n📊 Analysis and Recommendations:")
        print("-" * 40)
        
        processor = AudioProcessor()
        
        # Get energy levels
        bg_result = processor.detect_voice_activity(background_audio, threshold=0.01)
        speech_result = processor.detect_voice_activity(speech_audio, threshold=0.01)
        
        bg_energy = bg_result.outputs.get('energy', 0)
        speech_energy = speech_result.outputs.get('energy', 0)
        
        print(f"Background noise energy: {bg_energy:.6f}")
        print(f"Speech energy: {speech_energy:.6f}")
        print(f"Energy ratio (speech/background): {speech_energy/bg_energy:.2f}x")
        
        recommended = recommend_threshold(bg_energy, speech_energy)
        if recommended:
            print(f"\n💡 Recommended VAD threshold: {recommended:.3f}")
            print(f"Current threshold: {current_threshold:.3f}")
            
            if recommended > current_threshold:
                print("⚠️  Your current threshold may be too sensitive (causing false positives)")
                print(f"   Consider increasing VAD_THRESHOLD to {recommended:.3f} in your .env file")
            elif recommended < current_threshold * 0.5:
                print("⚠️  Your current threshold may be too high (might miss speech)")
                print(f"   Consider decreasing VAD_THRESHOLD to {recommended:.3f} in your .env file")
            else:
                print("✅ Your current threshold appears to be in a good range")
        
        print(f"\n🔧 To update your configuration:")
        print(f"   1. Edit your .env file")
        print(f"   2. Set: VAD_THRESHOLD={recommended:.3f}")
        print(f"   3. Restart your voice agent")
    
    print("\n🎯 Additional Tips:")
    print("• Higher thresholds = less sensitive (fewer false positives, might miss quiet speech)")
    print("• Lower thresholds = more sensitive (catches quiet speech, more false positives)")
    print("• Test in your actual environment (same room, background noise level)")
    print("• Consider using a headset microphone to reduce background noise")


if __name__ == "__main__":
    asyncio.run(main())
