import asyncio
import os
from dotenv import load_dotenv

from core.state_manager.state_manager import StateManager
from agents.orchestrator_agent_v3 import OrchestratorV3

load_dotenv()

async def select_input_device():
    import sounddevice as sd
    devices = sd.query_devices()
    input_devices = [(i, d) for i, d in enumerate(devices) if d['max_input_channels'] > 0]
    print("Available input devices:")
    for idx, dev in input_devices:
        print(f"  [{idx}] {dev['name']} (inputs: {dev['max_input_channels']})")
    while True:
        try:
            device_index = int(input("Enter the device index for your microphone: ").strip())
            if any(idx == device_index for idx, _ in input_devices):
                return device_index
            else:
                print("Invalid index. Please select from the list above.")
        except Exception:
            print("Please enter a valid integer index.")

async def record_audio_wav(duration_sec=3, sample_rate=16000, device_index=None):
    import sounddevice as sd
    import numpy as np
    import wave
    print(f"[MIC] Recording {duration_sec} seconds of audio...")
    audio = sd.rec(int(duration_sec * sample_rate), samplerate=sample_rate, channels=1, dtype='int16', device=device_index)
    sd.wait()
    audio_bytes = audio.tobytes()
    with wave.open('last_recorded.wav', 'wb') as wf:
        wf.setnchannels(1)
        wf.setsampwidth(2)
        wf.setframerate(sample_rate)
        wf.writeframes(audio_bytes)
    print(f"[INFO] Saved your recorded audio to: last_recorded.wav")
    return 'last_recorded.wav'

async def run_audio_interrupt_orchestrator():
    device_index = None
    print("Choose input method:")
    print("1. Record audio from microphone")
    print("2. Provide path to audio file")
    choice = input("Enter your choice (1 or 2): ").strip()
    if choice == "1":
        device_index = await select_input_device()
        audio_path = await record_audio_wav(device_index=device_index)
    elif choice == "2":
        path = input("Enter path to audio file (.wav or .mp3): ").strip()
        if not os.path.exists(path):
            print("File not found.")
            return
        audio_path = path
    else:
        print("Invalid choice.")
        return

    # Set up session/workflow
    workflow_name = "banking_workflow.json"  # or your desired workflow
    session_id = "audio_interrupt_test"
    user_id = "audio_interrupt_user"

    # Create StateManager
    state_manager = await StateManager.create(workflow_name, session_id, user_id)
    memory_manager = state_manager.memory_manager
    redis_client = state_manager.redis_client

    await memory_manager.set("contextual", "audio_path", audio_path)

    # Create and run the orchestrator
    orchestrator = OrchestratorV3(session_id, workflow_name, state_manager, memory_manager, redis_client)
    result = await orchestrator.run()
    print("[ORCHESTRATOR RESULT]", result)

if __name__ == "__main__":
    import sys
    import os
    sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))
    asyncio.run(run_audio_interrupt_orchestrator()) 