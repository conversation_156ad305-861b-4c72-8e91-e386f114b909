import sys
import asyncio
import os
import time
from pathlib import Path
from dotenv import load_dotenv
# Add project root to path for imports
project_root = str(Path(__file__).parent.parent)
sys.path.append(project_root)

load_dotenv()

from core.state_manager.state_manager import StateManager
from core.logging.logger_config import setup_development_logging, get_module_logger, cleanup_logger
from core.memory.redis_context import RedisClient
from schemas.outputSchema import StateOutput, StatusType, StatusCode
from schemas.a2a_message import A2AMessage, MessageType

logger = get_module_logger("test_state_manager_agents_v2", session_id="test_state_manager_agents_v2")

async def run_trial(sessionId: str = "test_session", userId: str = "user_1"):
    try:
        sm = await StateManager.create("banking_workflow.json", sessionId, userId)
        
        # test 1 - test getting a workflow
        workflow = await sm.getWorkflow()
        print("Workflow:", workflow)

        # test 2 - test getting all pipelines
        pipelines = await sm.getFullPipelineMap()
        print("Pipelines:", pipelines)

        # test 3 - test getting the current workflow state
        current_state = await sm.getCurrentWorkflowState()
        print("Current State:", current_state)

        # test 4 - test getting the current pipeline state
        current_pipeline_state = await sm.getCurrentPipelineState()
        print("Current Pipeline State:", current_pipeline_state)

        # test 5 - test executing a pipeline
        pipeline = await sm.getCurrentPipeline()
        print("Current Pipeline:", pipeline)

        # test 6 - test getting prohibited actions
        prohibited_actions = await sm.getProhibitedActions()
        print("Prohibited Actions:", prohibited_actions)

        # test 7 - test getting allowed actions
        allowed_actions = await sm.getAllowedActions()
        print("Allowed Actions:", allowed_actions)

        # test 8 - execution 

        # Step 1: Simulate greeting state
        
        greeting_tts_input = {"text": "Hello, how can I assist you today?"}
        greeting_tts_result = await sm.executePipelineState(greeting_tts_input)
        print("\ngreeting_tts_result:", greeting_tts_result)
        input("Press Enter to continue...")  # Debug pause

        # Step 2: Simulate state 2
        await sm.transitionWorkflow("Iniquiry")
        iniquiry_stt_input = {"audio_path": "fillerWords/user_conversation_part_1.mp3"}
        iniquiry_stt_result = await sm.executePipelineState(iniquiry_stt_input)
        print("\niniquiry_stt_result:", iniquiry_stt_result)
        input("Press Enter to continue...")  # Debug pause


        # go to the preprocessing state
        await sm.transitionPipeline("preprocessing")
        iniquiry_preprocessing_input = {}
        iniquiry_preprocessing_result = await sm.executePipelineState(iniquiry_preprocessing_input)
        print("\niniquiry_preprocessing_result:", iniquiry_preprocessing_result)
        input("Press Enter to continue...")  # Debug pause


        await sm.transitionPipeline("filler_tts")
        iniquiry_filler_tts_input = {}
        iniquiry_filler_tts_result = await sm.executePipelineState(iniquiry_filler_tts_input)
        print("\niniquiry_filler_tts_result:", iniquiry_filler_tts_result)
        input("Press Enter to continue...")  # Debug pause


        # go to the processing state
        await sm.transitionPipeline("processing")
        iniquiry_processing_input = {}
        iniquiry_processing_result = await sm.executePipelineState(iniquiry_processing_input)
        print("\niniquiry_processing_result:", iniquiry_processing_result)
        input("Press Enter to continue...")  # Debug pause


        # go to the tts state
        await sm.transitionPipeline("tts")
        iniquiry_tts_input = {}
        iniquiry_tts_result = await sm.executePipelineState(iniquiry_tts_input)
        print("\niniquiry_tts_result:", iniquiry_tts_result)
        input("Press Enter to continue...")  # Debug pause

        # Step 2: Simulate state 2
        await sm.transitionWorkflow("CheckBalance")
        check_balance_stt_input = {"audio_path": "fillerWords/user_conversation_part_1.mp3"}
        check_balance_stt_result = await sm.executePipelineState(check_balance_stt_input)
        print("\ncheck_balance_stt_result:", check_balance_stt_result)
        input("Press Enter to continue...")  # Debug pause


        # go to the preprocessing state
        await sm.transitionPipeline("preprocessing")
        check_balance_preprocessing_input = {}
        check_balance_preprocessing_result = await sm.executePipelineState(check_balance_preprocessing_input)
        print("\ncheck_balance_preprocessing_result:", check_balance_preprocessing_result)
        input("Press Enter to continue...")  # Debug pause


        await sm.transitionPipeline("filler_tts")
        check_balance_filler_tts_input = {}
        check_balance_filler_tts_result = await sm.executePipelineState(check_balance_filler_tts_input)
        print("\ncheck_balance_filler_tts_result:", check_balance_filler_tts_result)
        input("Press Enter to continue...")  # Debug pause


        # go to the processing state
        await sm.transitionPipeline("processing")
        check_balance_processing_input = {}
        check_balance_processing_result = await sm.executePipelineState(check_balance_processing_input)
        print("\ncheck_balance_processing_result:", check_balance_processing_result)
        input("Press Enter to continue...")  # Debug pause


        # go to the tts state
        await sm.transitionPipeline("tts")
        check_balance_tts_input = {}
        check_balance_tts_result = await sm.executePipelineState(check_balance_tts_input)
        print("\ncheck_balance_tts_result:", check_balance_tts_result)
        input("Press Enter to continue...")  # Debug pause

        # go to goodbye state
        await sm.transitionWorkflow("Goodbye")
        goodbye_tts_input = {"text": "Thank you for using our service. Goodbye!"}
        goodbye_tts_result = await sm.executePipelineState(goodbye_tts_input)
        print("\ngoodbye_tts_result:", goodbye_tts_result)

    except Exception as e:
        logger.error(f"Error during trial setup: {e}")
        return StateOutput(
            status=StatusType.ERROR,
            message=str(e),
            code=StatusCode.INTERNAL_ERROR,
            outputs={},
            meta={"source": "test_suite"}
        )
    
if __name__ == "__main__":
    try:
        setup_development_logging()
        asyncio.run(run_trial())
    except Exception as e:
        logger.error(f"Error in main execution: {e}")
    finally:
        cleanup_logger()  # Ensure logger is cleaned up properly

