from pathlib import Path
import sys
import os

project_root = str(Path(__file__).parent.parent)
sys.path.append(project_root)
import unittest
from unittest.mock import patch, AsyncMock, MagicMock
from core.state_mgmt.StateManager import StateManager
from schemas.outputSchema import StateOutput, StatusType, StatusCode
from core.exceptions import WorkflowError

class TestStateManager(unittest.IsolatedAsyncioTestCase):
    async def asyncSetUp(self):
        # Patch config loader and dependencies
        patcher_config = patch('core.state_mgmt.StateManager.load_config', new_callable=AsyncMock)
        self.mock_load_config = patcher_config.start()
        self.mock_load_config.return_value = {
            "paths": {
                "workflow_states_dir": "core/state_mgmt/states/workflow_states",
                "layer2_config_dir": "config"
            }
        }
        self.addCleanup(patcher_config.stop)
        # Patch MemoryManager
        patcher_memory = patch('core.state_mgmt.StateManager.MemoryManager', autospec=True)
        self.mock_memory_manager_cls = patcher_memory.start()
        self.mock_memory_manager = AsyncMock()
        self.mock_memory_manager_cls.return_value = self.mock_memory_manager
        self.addCleanup(patcher_memory.stop)
        # Patch WorkflowSchemaLoader and Layer2SchemaLoader
        patcher_workflow_loader = patch('core.state_mgmt.StateManager.WorkflowSchemaLoader', autospec=True)
        self.mock_workflow_loader = patcher_workflow_loader.start()
        self.mock_workflow = MagicMock()
        self.mock_workflow.workflow.start = 'state1'
        self.mock_workflow.workflow.states = {
            'state1': MagicMock(id='state1', layer2_id='l2id', expected_input=['input'], expected_output=['output'], transitions=[]),
            'state2': MagicMock(id='state2', layer2_id='l2id', expected_input=['input'], expected_output=['output'], transitions=[])
        }
        self.mock_workflow_loader.load = AsyncMock(return_value=self.mock_workflow)
        self.addCleanup(patcher_workflow_loader.stop)
        patcher_layer2_loader = patch('core.state_mgmt.StateManager.Layer2SchemaLoader', autospec=True)
        self.mock_layer2_loader = patcher_layer2_loader.start()
        self.mock_layer2 = MagicMock()
        self.mock_layer2.pipeline = [MagicMock(step='step1')]
        self.mock_layer2_loader.load = AsyncMock(return_value=self.mock_layer2)
        self.addCleanup(patcher_layer2_loader.stop)
        # Patch State
        patcher_state = patch('core.state_mgmt.StateManager.State', autospec=True)
        self.mock_state_cls = patcher_state.start()
        self.mock_state = AsyncMock()
        self.mock_state_cls.return_value = self.mock_state
        self.addCleanup(patcher_state.stop)
        # Patch logger
        patcher_logger = patch('core.state_mgmt.StateManager.logger', autospec=True)
        self.mock_logger = patcher_logger.start()
        self.addCleanup(patcher_logger.stop)
        # Patch os.path.isfile
        patcher_isfile = patch('os.path.isfile', return_value=True)
        self.mock_isfile = patcher_isfile.start()
        self.addCleanup(patcher_isfile.stop)

    async def test_create_initializes_state_manager(self):

        sm = await StateManager.create('workflow.json', 'sess1', 'user1')
        self.assertEqual(sm.workflow_name, 'workflow.json')
        self.assertEqual(sm.session_id, 'sess1')
        self.assertEqual(sm.user_id, 'user1')
        self.assertEqual(sm.current_state_id, 'state1')
        self.assertIn('l2id', sm.layer2_map)
        self.assertIsNotNone(sm.memory_manager)
        self.assertIsNotNone(sm.agent_registry)

    async def test_create_with_invalid_workflow_raises(self):
        self.mock_workflow_loader.load = AsyncMock(side_effect=WorkflowError("bad workflow"))
        with self.assertRaises(WorkflowError):
            await StateManager.create('bad_workflow.json', 'sess1', 'user1')

    async def test_execute_step_success(self):
        sm = await StateManager.create('workflow.json', 'sess1', 'user1')
        # Mock state.execute to return a successful StateOutput
        so = StateOutput(status=StatusType.SUCCESS, message="ok", code=StatusCode.OK, outputs={'output': 1}, meta={})
        self.mock_state.execute = AsyncMock(return_value=so)
        self.mock_memory_manager.set = AsyncMock()
        result = await sm.execute_step({'input': 123})
        self.assertIsInstance(result, StateOutput)
        self.assertEqual(result.status, StatusType.SUCCESS)
        self.mock_memory_manager.set.assert_awaited_with('contextual', 'output', {'output': 1})
        self.assertIn(result, sm.execution_history)

    async def test_execute_step_error(self):
        sm = await StateManager.create('workflow.json', 'sess1', 'user1')
        self.mock_state.execute = AsyncMock(side_effect=Exception("fail"))
        with self.assertRaises(Exception):
            await sm.execute_step({'input': 123})

    async def test_transition_to_next_state_success(self):
        sm = await StateManager.create('workflow.json', 'sess1', 'user1')
        # Add a transition to state1
        transition = MagicMock()
        transition.evaluate = AsyncMock(return_value=True)
        transition.target = 'state2'
        sm.get_state = MagicMock(return_value=MagicMock(transitions=[transition]))
        self.mock_memory_manager.get = AsyncMock(return_value={'foo': 'bar'})
        next_state = await sm.transition('state2')
        self.assertEqual(next_state, 'state2')
        self.assertEqual(sm.current_state_id, 'state2')

    async def test_transition_to_next_state_no_transitions(self):
        sm = await StateManager.create('workflow.json', 'sess1', 'user1')
        sm.get_state = MagicMock(return_value=MagicMock(transitions=[]))
        self.mock_memory_manager.get = AsyncMock(return_value=None)
        result = await sm.transition("state3")
        self.assertIsNone(result)

    async def test_transition_to_next_state_not_found(self):
        sm = await StateManager.create('workflow.json', 'sess1', 'user1')
        sm.get_state = MagicMock(return_value=None)
        with self.assertRaises(WorkflowError):
            await sm.transition("state3")

    async def test_end_session_cleanup(self):
        sm = await StateManager.create('workflow.json', 'sess1', 'user1')
        self.mock_memory_manager.clear_contextual = AsyncMock()
        self.mock_memory_manager.clear_ephemeral = AsyncMock()
        await sm.end_session_cleanup()
        self.mock_memory_manager.clear_contextual.assert_awaited()
        self.mock_memory_manager.clear_ephemeral.assert_awaited()

    async def test_memory_manager_integration(self):
        sm = await StateManager.create('workflow.json', 'sess1', 'user1')
        self.mock_memory_manager.set = AsyncMock()
        self.mock_memory_manager.get = AsyncMock(return_value='val')
        await self.mock_memory_manager.set('contextual', 'foo', 'bar')
        val = await self.mock_memory_manager.get('foo')
        self.assertEqual(val, 'val')

    async def test_state_with_multiple_transitions(self):
        sm = await StateManager.create('workflow.json', 'sess1', 'user1')
        t1 = MagicMock()
        t1.evaluate = AsyncMock(return_value=False)
        t2 = MagicMock()
        t2.evaluate = AsyncMock(return_value=True)
        t2.target = 'state2'
        sm.get_state = MagicMock(return_value=MagicMock(transitions=[t1, t2]))
        self.mock_memory_manager.get = AsyncMock(return_value={'foo': 'bar'})
        next_state = await sm.transition('state2')
        self.assertEqual(next_state, 'state2')
        self.assertEqual(sm.current_state_id, 'state2')

    async def test_layer2_loading_failure(self):
        self.mock_layer2_loader.load = AsyncMock(side_effect=Exception("Layer2 fail"))
        # Layer2 loading failures are handled gracefully and don't prevent StateManager creation
        sm = await StateManager.create('workflow.json', 'sess1', 'user1')
        # Verify that the StateManager was created successfully despite Layer2 loading failure
        self.assertIsNotNone(sm)
        self.assertEqual(sm.workflow_name, 'workflow.json')
        # Verify that the failed Layer2 is not in the layer2_map
        self.assertEqual(len(sm.layer2_map), 0)

    async def test_handle_intent_goodbye_triggers_cleanup(self):
        sm = await StateManager.create('workflow.json', 'sess1', 'user1')
        sm.end_session_cleanup = AsyncMock()
        await sm.handle_intent('goodbye')
        sm.end_session_cleanup.assert_awaited()

    async def test_execute_step_missing_input_error(self):
        sm = await StateManager.create('workflow.json', 'sess1', 'user1')
        # Simulate State raising WorkflowError for missing input
        from core.exceptions import WorkflowError
        self.mock_state.execute = AsyncMock(side_effect=WorkflowError("Missing input"))
        with self.assertRaises(WorkflowError):
            await sm.execute_step({})

if __name__ == "__main__":
    unittest.main()
