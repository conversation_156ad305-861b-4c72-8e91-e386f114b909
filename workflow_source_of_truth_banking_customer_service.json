{"id": "banking_customer_service", "name": "Banking Customer Service", "version": "1.0", "states": {"Greeting": {"type": "input", "layer2_id": "l2_greeting_banking_system", "allowed_tools": ["STT", "LLM", "TTS", "CACHE"], "expected_input": [], "expected_output": ["audio_path", "latencyTTS"]}, "CheckBalance": {"type": "inform", "layer2_id": "l2_account_balance", "allowed_tools": ["LLM", "TTS", "DB_QUERY"], "expected_input": ["account_id"], "expected_output": ["account_balance"]}, "TransferFunds": {"type": "transaction", "layer2_id": "l2_fund_transfer", "allowed_tools": ["LLM", "TTS", "TRANSACTION_API"], "expected_input": ["source_account", "target_account", "amount"], "expected_output": ["confirmation_number"]}, "LoanApplication": {"type": "process", "layer2_id": "l2_loan_application", "allowed_tools": ["LLM", "TTS", "CREDIT_CHECK"], "expected_input": ["income", "credit_score", "requested_amount"], "expected_output": ["loan_approval_status", "interest_rate"]}, "LoanApproved": {"type": "inform", "layer2_id": "l2_loan_approved", "allowed_tools": ["LLM", "TTS"], "expected_input": [], "expected_output": ["next_steps"]}, "LoanDenied": {"type": "inform", "layer2_id": "l2_loan_denied", "allowed_tools": ["LLM", "TTS"], "expected_input": [], "expected_output": ["denial_reason"]}, "OfferMoreHelp": {"type": "decision", "layer2_id": "l2_more_help", "allowed_tools": ["STT", "LLM", "TTS"], "expected_input": [], "expected_output": ["user_response"]}, "Goodbye": {"type": "end", "layer2_id": "l2_goodbye_banking_system", "allowed_tools": ["TTS"], "expected_input": [], "expected_output": ["exit_signal"]}}, "rules": [{"from": "Greeting", "condition": "intent == 'account_balance'", "to": "CheckBalance"}, {"from": "Greeting", "condition": "intent == 'fund_transfer'", "to": "TransferFunds"}, {"from": "Greeting", "condition": "intent == 'loan_application'", "to": "LoanApplication"}, {"from": "CheckBalance", "condition": "true", "to": "OfferMoreHelp"}, {"from": "TransferFunds", "condition": "true", "to": "OfferMoreHelp"}, {"from": "LoanApplication", "condition": "loan_approval_status == 'approved'", "to": "LoanApproved"}, {"from": "LoanApplication", "condition": "loan_approval_status == 'denied'", "to": "LoanDenied"}, {"from": "LoanApproved", "condition": "true", "to": "OfferMoreHelp"}, {"from": "LoanDenied", "condition": "true", "to": "OfferMoreHelp"}, {"from": "OfferMoreHelp", "condition": "user_response == 'no'", "to": "Goodbye"}], "allowed_actions": ["Check account balance", "Transfer funds", "Apply for loan", "Report lost card", "Update personal information"], "prohibited_actions": ["Do not share PINs or passwords", "Do not process transactions without verification", "Do not disclose sensitive account details"], "allowed_tools": ["STT", "CACHE", "LLM", "TTS", "CREDIT_CHECK", "TRANSACTION_API", "DB_QUERY"]}