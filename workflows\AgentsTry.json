{"workflow": {"id": "stt_test_workflow", "name": "STT Agent Test Flow", "version": "1.0", "allowed_actions": [], "prohibited_actions": [], "start": "state_greeting", "states": {"state_greeting": {"id": "state_greeting", "type": "input", "layer2_id": "l2_ai_greeting", "expected_input": ["audio_path"], "expected_output": ["intent", "llm_answer", "audio_path"], "transitions": [{"condition": "always", "target": "state_2"}], "allowed_tools": ["STT"]}, "state_2": {"id": "state_2", "type": "input", "layer2_id": "l2_state_2", "expected_input": ["audio_path"], "expected_output": ["intent", "llm_answer", "audio_path"], "transitions": [{"condition": "always", "target": "state_3"}], "allowed_tools": ["STT"]}, "state_3": {"id": "state_3", "type": "input", "layer2_id": "l2_state_3", "expected_input": ["audio_path"], "expected_output": ["intent", "llm_answer", "audio_path"], "transitions": [], "allowed_tools": ["STT"]}}}}