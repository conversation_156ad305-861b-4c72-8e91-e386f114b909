{"workflow": {"id": "banking_customer_service", "name": "Banking Customer Service", "version": "1.0", "start": "Greeting", "allowed_actions": ["Check account balance", "Transfer funds", "Apply for loan", "Report lost card", "Update personal information"], "prohibited_actions": ["Do not share PINs or passwords", "Do not process transactions without verification", "Do not disclose sensitive account details"], "states": {"Greeting": {"id": "Greeting", "type": "input", "layer2_id": "l2_greeting_banking_system_v2", "expected_input": [], "expected_output": ["audio_path", "latencyTTS"], "transitions": [{"condition": "true", "target": "Iniquiry"}], "allowed_tools": ["STT", "LLM", "TTS", "CACHE"]}, "Iniquiry": {"id": "Iniquiry", "type": "inform", "layer2_id": "l2_iniquiry_banking_system_v2", "expected_input": ["text"], "expected_output": ["audio_path", "latencyTTS"], "transitions": [{"condition": "intent == 'account_balance'", "target": "CheckBalance"}, {"condition": "intent == 'fund_transfer'", "target": "TransferFunds"}, {"condition": "intent == 'exchange_rate'", "target": "exchangeRate"}, {"condition": "intent == 'goodbye'", "target": "Goodbye"}], "allowed_tools": ["STT", "LLM", "TTS", "CACHE"]}, "CheckBalance": {"id": "CheckBalance", "type": "inform", "layer2_id": "l2_check_balance_banking_system_v2", "expected_input": ["account_id"], "expected_output": ["audio_path", "latencyTTS"], "transitions": [], "allowed_tools": ["LLM", "TTS", "DB_QUERY"]}, "TransferFunds": {"id": "TransferFunds", "type": "transaction", "layer2_id": "l2_transfer_funds_banking_system_v2", "expected_input": ["source_account", "target_account", "amount"], "expected_output": ["audio_path", "latencyTTS"], "transitions": [], "allowed_tools": ["LLM", "TTS", "TRANSACTION_API"]}, "exchangeRate": {"id": "exchangeRate", "type": "transaction", "layer2_id": "l2_exchange_rate_banking_system_v2", "expected_input": ["source_account", "target_account", "amount"], "expected_output": ["audio_path", "latencyTTS"], "transitions": [], "allowed_tools": ["LLM", "TTS", "TRANSACTION_API"]}, "Goodbye": {"id": "Goodbye", "type": "end", "layer2_id": "l2_goodbye_banking_system_v2", "expected_input": [], "expected_output": ["audio_path", "latencyTTS"], "transitions": [], "allowed_tools": ["TTS"]}}}}