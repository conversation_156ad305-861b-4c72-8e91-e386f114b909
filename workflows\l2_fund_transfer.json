{"id": "l2_fund_transfer", "version": "1.0", "pipeline": [{"step": "stt", "process": "stt_process", "agent": "stt_agent", "input": {"audio": "audio_path"}, "tools": {"external_tools": "stt"}, "output": {"text": "text", "latencySTT": "latencySTT", "audio_path": "audio_path"}}, {"step": "preprocessing", "process": "preprocessing_process", "agent": "preprocessing_agent", "input": {"text": "text"}, "tools": {"external_tools": "openai"}, "output": {"source_account": "source_account", "target_account": "target_account", "amount": "amount", "latencyPreprocessing": "latencyPreprocessing", "fallback_message": "fallback_message"}}, {"step": "filler_tts", "process": "filler_tts_process", "agent": "filler_tts_agent", "input": {"fallback_message": "fallback_message"}, "tools": {"external_tools": "openai"}, "output": {"audio_path": "audio_path", "latencyFillerTTS": "latencyFillerTTS"}}, {"step": "processing", "process": "processing_process", "agent": "processing_agent", "input": {"source_account": "source_account", "target_account": "target_account", "amount": "amount"}, "tools": {"external_tools": "transaction_processor"}, "output": {"confirmation_number": "confirmation_number", "transaction_status": "transaction_status", "latencyProcessing": "latencyProcessing"}}, {"step": "tts", "process": "tts_process", "agent": "tts_agent", "input": {"text": "confirmation_number"}, "tools": {"external_tools": "openai"}, "output": {"audio_path": "audio_path", "latencyTTS": "latencyTTS"}}], "onInterrupt": {"handler": "interrupt_manager", "resume_from": "stt"}, "onError": {"retry": 3, "fallback_state": "l2_fallback_transaction"}}