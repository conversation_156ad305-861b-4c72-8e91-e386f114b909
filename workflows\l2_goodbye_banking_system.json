{"id": "l2_goodbye", "version": "1.0", "pipeline": [{"step": "stt", "process": "stt_process", "input": {"audio": "audio_path"}, "tools": {"external_tools": "stt"}, "output": {"text": "text", "latencySTT": "latencySTT", "audio_path": "audio_path"}, "agent": "stt_agent"}, {"step": "preprocessing", "process": "preprocessing_process", "input": {"text": "text"}, "tools": {"external_tools": "openai"}, "output": {"sentiment": "sentiment", "latencyPreprocessing": "latencyPreprocessing", "fallback_message": "fallback_message"}, "agent": "preprocessing_agent"}, {"step": "filler_tts", "process": "filler_tts_process", "input": {"fallback_message": "fallback_message"}, "tools": {"external_tools": "openai"}, "output": {"audio_path": "audio_path", "latencyFillerTTS": "latencyFillerTTS"}, "agent": "filler_tts_agent"}, {"step": "processing", "process": "processing_process", "input": {"sentiment": "sentiment"}, "tools": {"external_tools": "openai"}, "output": {"personalized_goodbye": "personalized_goodbye", "exit_signal": "exit_signal", "latencyProcessing": "latencyProcessing"}, "agent": "processing_agent"}, {"step": "tts", "process": "tts_process", "input": {"text": "personalized_goodbye"}, "tools": {"external_tools": "openai"}, "output": {"audio_path": "audio_path", "latencyTTS": "latencyTTS"}, "agent": "tts_agent"}], "onInterrupt": {"handler": "interrupt_manager", "resume_from": "stt"}, "onError": {"retry": 1, "fallback_state": "l2_fallback_goodbye"}, "outputs": ["thank_you_message"]}