{"id": "l2_loan_approved", "version": "1.0", "pipeline": [{"step": "stt", "process": "stt_process", "input": {"audio": "audio_path"}, "tools": {"external_tools": "stt"}, "output": {"text": "text", "latencySTT": "latencySTT", "audio_path": "audio_path"}, "agent": "stt_agent"}, {"step": "preprocessing", "process": "preprocessing_process", "input": {"text": "text"}, "tools": {"external_tools": "openai"}, "output": {"acceptance_confirmation": "acceptance_confirmation", "latencyPreprocessing": "latencyPreprocessing", "fallback_message": "fallback_message"}, "agent": "preprocessing_agent"}, {"step": "filler_tts", "process": "filler_tts_process", "input": {"fallback_message": "fallback_message"}, "tools": {"external_tools": "openai"}, "output": {"audio_path": "audio_path", "latencyFillerTTS": "latencyFillerTTS"}, "agent": "filler_tts_agent"}, {"step": "processing", "process": "processing_process", "input": {"acceptance_confirmation": "acceptance_confirmation"}, "tools": {"external_tools": "document_generator"}, "output": {"next_steps": "next_steps", "document_reference": "document_reference", "latencyProcessing": "latencyProcessing"}, "agent": "processing_agent"}, {"step": "tts", "process": "tts_process", "input": {"text": "next_steps"}, "tools": {"external_tools": "openai"}, "output": {"audio_path": "audio_path", "latencyTTS": "latencyTTS"}, "agent": "tts_agent"}], "onInterrupt": {"handler": "interrupt_manager", "resume_from": "stt"}, "onError": {"retry": 2, "fallback_state": "l2_fallback_approved"}, "outputs": ["congratulations_message"]}