{"id": "l2_more_help", "version": "1.0", "pipeline": [{"step": "stt", "process": "stt_process", "input": {"audio": "audio_path"}, "tools": {"external_tools": "stt"}, "output": {"text": "text", "latencySTT": "latencySTT", "audio_path": "audio_path"}, "agent": "stt_agent"}, {"step": "preprocessing", "process": "preprocessing_process", "input": {"text": "text"}, "tools": {"external_tools": "openai"}, "output": {"user_response": "user_response", "latencyPreprocessing": "latencyPreprocessing", "fallback_message": "fallback_message"}, "agent": "preprocessing_agent"}, {"step": "filler_tts", "process": "filler_tts_process", "input": {"fallback_message": "fallback_message"}, "tools": {"external_tools": "openai"}, "output": {"audio_path": "audio_path", "latencyFillerTTS": "latencyFillerTTS"}, "agent": "filler_tts_agent"}, {"step": "processing", "process": "processing_process", "input": {"user_response": "user_response"}, "tools": {"external_tools": "context_analyzer"}, "output": {"routing_decision": "routing_decision", "suggested_services": "suggested_services", "latencyProcessing": "latencyProcessing"}, "agent": "processing_agent"}, {"step": "tts", "process": "tts_process", "input": {"text": "routing_decision"}, "tools": {"external_tools": "openai"}, "output": {"audio_path": "audio_path", "latencyTTS": "latencyTTS"}, "agent": "tts_agent"}], "onInterrupt": {"handler": "interrupt_manager", "resume_from": "stt"}, "onError": {"retry": 2, "fallback_state": "l2_fallback_more_help"}, "outputs": ["acknowledgement_message"]}