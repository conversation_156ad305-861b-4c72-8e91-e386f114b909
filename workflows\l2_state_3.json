{"id": "l2_state_3", "version": "1.0", "pipeline": [{"step": "stt", "process": "stt_process", "input": {"audio": "audio_path"}, "tools": {"external_tools": "stt"}, "output": {"text": "text", "latencySTT": "latencySTT", "audio_path": "audio_path"}, "agent": "stt_agent"}, {"step": "preprocessing", "process": "preprocessing_process", "input": {"text": "text"}, "tools": {"external_tools": "openai"}, "output": {"fallback_message": "fallback_message", "emotion": "emotion", "gender": "gender", "intent": "intent", "latencyPreprocessing": "latencyPreprocessing", "clean_text": "clean_text"}, "agent": "preprocessing_agent"}, {"step": "filler_tts", "process": "filler_tts_process", "input": {"fallback_message": "fallback_message"}, "tools": {"external_tools": "openai"}, "output": {"audio_path": "audio_path", "latencyFillerTTS": "latencyFillerTTS"}, "agent": "filler_tts_agent"}, {"step": "processing", "process": "processing_process", "input": {"text": "clean_text", "intent": "intent", "emotion": "emotion", "gender": "gender"}, "tools": {"external_tools": "openai"}, "output": {"route": "route", "data": "data", "llm_answer": "llm_answer", "latencyProcessing": "latencyProcessing"}, "agent": "processing_agent"}, {"step": "tts", "process": "tts_process", "input": {"text": "llm_answer"}, "tools": {"external_tools": "openai"}, "output": {"audio_path": "audio_path", "latencyTTS": "latencyTTS"}, "agent": "tts_agent"}], "onInterrupt": {"handler": "interrupt_manager", "resume_from": "stt"}, "onError": {"retry": 2, "fallback_state": "l2_fallback_generic"}}